import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Layout from './components/Layout/Layout';
import AuthPage from './pages/AuthPage';
import DashboardPage from './pages/DashboardPage';
import ProgressPage from './pages/ProgressPage';
import PhotosPage from './pages/PhotosPage';
import ForumPage from './pages/ForumPage';
import SubscriptionPage from './pages/SubscriptionPage';
import MedicalRecordPage from './pages/MedicalRecordPage';
import TeleconsultationPage from './pages/TeleconsultationPage';
import GamificationPage from './pages/GamificationPage';
import RemindersPage from './pages/RemindersPage';
import AnalyticsPage from './pages/AnalyticsPage';
import EducationPage from './pages/EducationPage';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  const { user } = useAuth();

  // Register service worker for PWA
  React.useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    }
  }, []);

  if (!user) {
    return (
      <Routes>
        <Route path="/auth" element={<AuthPage />} />
        <Route path="*" element={<Navigate to="/auth\" replace />} />
      </Routes>
    );
  }

  return (
    <Routes>
      <Route path="/auth" element={<Navigate to="/dashboard\" replace />} />
      <Route path="/" element={<Layout />}>
        <Route index element={<Navigate to="/dashboard\" replace />} />
        <Route path="dashboard" element={<DashboardPage />} />
        
        {/* Patient Routes */}
        {user.role === 'patient' && (
          <>
            <Route path="progress\" element={<ProgressPage />} />
            <Route path="photos" element={<PhotosPage />} />
            <Route path="medical-record" element={<MedicalRecordPage />} />
            <Route path="teleconsultation" element={<TeleconsultationPage />} />
            <Route path="gamification" element={<GamificationPage />} />
            <Route path="reminders" element={<RemindersPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
            <Route path="education" element={<EducationPage />} />
          </>
        )}
        
        {/* Practitioner Routes */}
        {user.role === 'practitioner' && (
          <>
            <Route path="patients" element={<div>Patients Page</div>} />
            <Route path="appointments" element={<div>Appointments Page</div>} />
            <Route path="subscription" element={<SubscriptionPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
          </>
        )}
        
        {/* Common Routes */}
        <Route path="forum" element={<ForumPage />} />
        <Route path="profile" element={<div>Profile Page</div>} />
        
        {/* Admin Routes */}
        {user.role === 'admin' && (
          <>
            <Route path="admin" element={<div>Admin Dashboard</div>} />
            <Route path="admin/vip" element={<div>VIP Management</div>} />
          </>
        )}
      </Route>
      <Route path="*" element={<Navigate to="/dashboard\" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <Router>
          <div className="App">
            <AppRoutes />
          </div>
        </Router>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;