import toast from 'react-hot-toast'

// Types pour les notifications
export interface NotificationOptions {
  title: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// Système de notifications toast amélioré
export const notify = {
  success: (message: string, options?: Partial<NotificationOptions>) => {
    toast.success(message, {
      duration: options?.duration || 4000,
      position: 'top-right',
      style: {
        background: '#10B981',
        color: '#fff',
        borderRadius: '12px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#10B981'
      }
    })
  },

  error: (message: string, options?: Partial<NotificationOptions>) => {
    toast.error(message, {
      duration: options?.duration || 6000,
      position: 'top-right',
      style: {
        background: '#EF4444',
        color: '#fff',
        borderRadius: '12px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#EF4444'
      }
    })
  },

  warning: (message: string, options?: Partial<NotificationOptions>) => {
    toast(message, {
      duration: options?.duration || 5000,
      position: 'top-right',
      icon: '⚠️',
      style: {
        background: '#F59E0B',
        color: '#fff',
        borderRadius: '12px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  },

  info: (message: string, options?: Partial<NotificationOptions>) => {
    toast(message, {
      duration: options?.duration || 4000,
      position: 'top-right',
      icon: 'ℹ️',
      style: {
        background: '#3B82F6',
        color: '#fff',
        borderRadius: '12px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  },

  achievement: (message: string, points: number) => {
    toast.success(`🏆 ${message} (+${points} points)`, {
      duration: 6000,
      position: 'top-center',
      style: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: '#fff',
        borderRadius: '16px',
        padding: '20px',
        fontSize: '16px',
        fontWeight: '600',
        minWidth: '300px',
        textAlign: 'center'
      },
      iconTheme: {
        primary: '#FFD700',
        secondary: '#fff'
      }
    })
  },

  custom: (component: React.ReactNode, options?: any) => {
    toast.custom(component, {
      duration: options?.duration || 4000,
      position: options?.position || 'top-right',
      ...options
    })
  }
}

// Notifications push natives
export class PushNotificationManager {
  private static instance: PushNotificationManager
  private permission: NotificationPermission = 'default'

  static getInstance(): PushNotificationManager {
    if (!PushNotificationManager.instance) {
      PushNotificationManager.instance = new PushNotificationManager()
    }
    return PushNotificationManager.instance
  }

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications')
      return false
    }

    if (this.permission === 'granted') {
      return true
    }

    const permission = await Notification.requestPermission()
    this.permission = permission
    return permission === 'granted'
  }

  async sendNotification(options: {
    title: string
    body: string
    icon?: string
    badge?: string
    tag?: string
    data?: any
    actions?: Array<{
      action: string
      title: string
      icon?: string
    }>
  }): Promise<void> {
    if (this.permission !== 'granted') {
      const granted = await this.requestPermission()
      if (!granted) return
    }

    const notification = new Notification(options.title, {
      body: options.body,
      icon: options.icon || '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
      badge: options.badge || '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg',
      tag: options.tag,
      data: options.data,
      requireInteraction: true,
      silent: false
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
      if (options.data?.url) {
        window.location.href = options.data.url
      }
    }

    // Auto-close après 10 secondes
    setTimeout(() => {
      notification.close()
    }, 10000)
  }

  // Notifications spécifiques à l'orthodontie
  async sendWearReminder(): Promise<void> {
    await this.sendNotification({
      title: '🦷 Rappel OrthProgress',
      body: 'N\'oubliez pas de porter votre appareil orthodontique !',
      tag: 'wear-reminder',
      data: { url: '/progress' }
    })
  }

  async sendPhotoReminder(): Promise<void> {
    await this.sendNotification({
      title: '📸 Temps pour une photo !',
      body: 'Prenez une photo de vos progrès cette semaine',
      tag: 'photo-reminder',
      data: { url: '/photos' }
    })
  }

  async sendAppointmentReminder(appointmentDate: string): Promise<void> {
    await this.sendNotification({
      title: '📅 Rendez-vous orthodontique',
      body: `Votre rendez-vous est prévu le ${appointmentDate}`,
      tag: 'appointment-reminder',
      data: { url: '/appointments' }
    })
  }

  async sendAchievementNotification(achievement: string, points: number): Promise<void> {
    await this.sendNotification({
      title: '🏆 Nouveau succès débloqué !',
      body: `${achievement} (+${points} points)`,
      tag: 'achievement',
      data: { url: '/gamification' }
    })
  }

  async sendComplianceAlert(complianceRate: number): Promise<void> {
    if (complianceRate < 70) {
      await this.sendNotification({
        title: '⚠️ Attention à votre régularité',
        body: `Votre taux de conformité est de ${complianceRate}%. Essayez de porter votre appareil plus régulièrement.`,
        tag: 'compliance-alert',
        data: { url: '/analytics' }
      })
    }
  }
}

// Service Worker pour les notifications en arrière-plan
export const registerNotificationServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator && 'PushManager' in window) {
    try {
      const registration = await navigator.serviceWorker.register('/sw-notifications.js')
      console.log('Service Worker pour notifications enregistré:', registration)
      
      // Vérifier les permissions
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        console.log('Permissions de notification accordées')
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du Service Worker:', error)
    }
  }
}

// Planificateur de notifications intelligentes
export class SmartNotificationScheduler {
  private static instance: SmartNotificationScheduler
  private userHabits: { [key: string]: number[] } = {}
  private notificationManager = PushNotificationManager.getInstance()

  static getInstance(): SmartNotificationScheduler {
    if (!SmartNotificationScheduler.instance) {
      SmartNotificationScheduler.instance = new SmartNotificationScheduler()
    }
    return SmartNotificationScheduler.instance
  }

  // Analyser les habitudes de l'utilisateur
  analyzeUserHabits(progressData: { date: string; worn: boolean }[]): void {
    const habits: { [key: string]: number[] } = {}
    
    progressData.forEach(entry => {
      if (entry.worn) {
        const date = new Date(entry.date)
        const dayOfWeek = date.getDay()
        const hour = date.getHours()
        
        if (!habits[dayOfWeek]) {
          habits[dayOfWeek] = []
        }
        habits[dayOfWeek].push(hour)
      }
    })

    this.userHabits = habits
  }

  // Obtenir le meilleur moment pour envoyer une notification
  getBestNotificationTime(dayOfWeek: number): number {
    const dayHabits = this.userHabits[dayOfWeek]
    if (!dayHabits || dayHabits.length === 0) {
      return 9 // Heure par défaut: 9h
    }

    // Calculer la moyenne des heures où l'utilisateur porte son appareil
    const averageHour = dayHabits.reduce((sum, hour) => sum + hour, 0) / dayHabits.length
    
    // Envoyer la notification 1 heure avant l'heure habituelle
    return Math.max(8, Math.floor(averageHour) - 1)
  }

  // Planifier des notifications intelligentes
  scheduleSmartReminders(): void {
    const now = new Date()
    const currentDay = now.getDay()
    const currentHour = now.getHours()
    
    // Planifier pour aujourd'hui si pas encore fait
    const todayBestTime = this.getBestNotificationTime(currentDay)
    if (currentHour < todayBestTime) {
      const todayNotificationTime = new Date()
      todayNotificationTime.setHours(todayBestTime, 0, 0, 0)
      
      const timeUntilNotification = todayNotificationTime.getTime() - now.getTime()
      setTimeout(() => {
        this.notificationManager.sendWearReminder()
      }, timeUntilNotification)
    }

    // Planifier pour demain
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowDay = tomorrow.getDay()
    const tomorrowBestTime = this.getBestNotificationTime(tomorrowDay)
    
    const tomorrowNotificationTime = new Date(tomorrow)
    tomorrowNotificationTime.setHours(tomorrowBestTime, 0, 0, 0)
    
    const timeUntilTomorrowNotification = tomorrowNotificationTime.getTime() - now.getTime()
    setTimeout(() => {
      this.notificationManager.sendWearReminder()
      this.scheduleSmartReminders() // Replanifier pour le jour suivant
    }, timeUntilTomorrowNotification)
  }
}
