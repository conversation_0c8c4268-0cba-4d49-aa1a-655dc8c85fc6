import { useState, useEffect } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase, supabaseHelpers, Database } from '../lib/supabase'
import { notify } from '../lib/notifications'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthState {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  error: AuthError | null
}

interface SignUpData {
  email: string
  password: string
  fullName: string
  role: 'patient' | 'practitioner'
  practitionerCode?: string
}

interface SignInData {
  email: string
  password: string
}

export const useSupabaseAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    session: null,
    loading: true,
    error: null
  })

  // Écouter les changements d'authentification
  useEffect(() => {
    // Obtenir la session initiale
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        setAuthState(prev => ({ ...prev, error, loading: false }))
        return
      }

      if (session?.user) {
        loadUserProfile(session.user.id).then(profile => {
          setAuthState({
            user: session.user,
            profile,
            session,
            loading: false,
            error: null
          })
        })
      } else {
        setAuthState(prev => ({ ...prev, loading: false }))
      }
    })

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session)

        if (event === 'SIGNED_IN' && session?.user) {
          const profile = await loadUserProfile(session.user.id)
          setAuthState({
            user: session.user,
            profile,
            session,
            loading: false,
            error: null
          })
          notify.success('Connexion réussie !')
        } else if (event === 'SIGNED_OUT') {
          setAuthState({
            user: null,
            profile: null,
            session: null,
            loading: false,
            error: null
          })
          notify.info('Déconnexion réussie')
        } else if (event === 'TOKEN_REFRESHED' && session) {
          setAuthState(prev => ({
            ...prev,
            session,
            user: session.user
          }))
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Charger le profil utilisateur
  const loadUserProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabaseHelpers.getProfile(userId)
      if (error) {
        console.error('Erreur lors du chargement du profil:', error)
        return null
      }
      return data
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error)
      return null
    }
  }

  // Inscription
  const signUp = async (signUpData: SignUpData) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      // Créer le compte utilisateur
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: signUpData.email,
        password: signUpData.password,
        options: {
          data: {
            full_name: signUpData.fullName,
            role: signUpData.role,
            practitioner_code: signUpData.practitionerCode
          }
        }
      })

      if (authError) {
        throw authError
      }

      if (authData.user) {
        // Créer le profil utilisateur
        const profileData: Database['public']['Tables']['profiles']['Insert'] = {
          id: authData.user.id,
          email: signUpData.email,
          full_name: signUpData.fullName,
          role: signUpData.role,
          practitioner_code: signUpData.practitionerCode || null,
          is_vip: signUpData.email === '<EMAIL>',
          subscription_status: signUpData.role === 'practitioner' ? 'trial' : null,
          trial_end_date: signUpData.role === 'practitioner' 
            ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() 
            : null
        }

        const { error: profileError } = await supabaseHelpers.createProfile(profileData)
        if (profileError) {
          console.error('Erreur lors de la création du profil:', profileError)
        }

        // Si c'est un patient, créer l'entrée patient
        if (signUpData.role === 'patient') {
          const { error: patientError } = await supabase
            .from('patients')
            .insert({
              user_id: authData.user.id,
              practitioner_id: null, // À assigner plus tard
              current_aligner: 1
            })

          if (patientError) {
            console.error('Erreur lors de la création du patient:', patientError)
          }
        }

        notify.success('Compte créé avec succès ! Vérifiez votre email.')
      }

      return { data: authData, error: null }
    } catch (error) {
      const authError = error as AuthError
      setAuthState(prev => ({ ...prev, error: authError, loading: false }))
      notify.error(authError.message || 'Erreur lors de l\'inscription')
      return { data: null, error: authError }
    }
  }

  // Connexion
  const signIn = async (signInData: SignInData) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      const { data, error } = await supabase.auth.signInWithPassword({
        email: signInData.email,
        password: signInData.password
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      const authError = error as AuthError
      setAuthState(prev => ({ ...prev, error: authError, loading: false }))
      notify.error(authError.message || 'Erreur lors de la connexion')
      return { data: null, error: authError }
    }
  }

  // Déconnexion
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        throw error
      }
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)
      notify.error('Erreur lors de la déconnexion')
    }
  }

  // Réinitialisation du mot de passe
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) {
        throw error
      }

      notify.success('Email de réinitialisation envoyé !')
      return { error: null }
    } catch (error) {
      const authError = error as AuthError
      notify.error(authError.message || 'Erreur lors de la réinitialisation')
      return { error: authError }
    }
  }

  // Mettre à jour le profil
  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!authState.user) {
        throw new Error('Utilisateur non connecté')
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', authState.user.id)
        .select()
        .single()

      if (error) {
        throw error
      }

      setAuthState(prev => ({ ...prev, profile: data }))
      notify.success('Profil mis à jour avec succès !')
      return { data, error: null }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error)
      notify.error('Erreur lors de la mise à jour du profil')
      return { data: null, error }
    }
  }

  // Vérifier si l'utilisateur est connecté
  const isAuthenticated = !!authState.user

  // Vérifier le rôle de l'utilisateur
  const hasRole = (role: 'patient' | 'practitioner' | 'admin') => {
    return authState.profile?.role === role
  }

  // Vérifier si l'utilisateur est VIP
  const isVip = authState.profile?.is_vip || false

  // Vérifier le statut d'abonnement
  const hasActiveSubscription = () => {
    if (authState.profile?.role === 'patient') return true // Les patients n'ont pas besoin d'abonnement
    return authState.profile?.subscription_status === 'active'
  }

  return {
    // État
    user: authState.user,
    profile: authState.profile,
    session: authState.session,
    loading: authState.loading,
    error: authState.error,

    // Actions
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile,

    // Utilitaires
    isAuthenticated,
    hasRole,
    isVip,
    hasActiveSubscription,
    loadUserProfile
  }
}
