export interface User {
  id: string;
  email: string;
  fullName: string;
  role: 'patient' | 'practitioner' | 'admin';
  practitionerCode?: string;
  isVip?: boolean;
  subscriptionStatus?: 'trial' | 'active' | 'expired';
  trialEndDate?: Date;
  createdAt: Date;
}

export interface Patient extends User {
  role: 'patient';
  practitionerCode: string;
  medicalRecord: MedicalRecord;
  progressData: ProgressData;
}

export interface Practitioner extends User {
  role: 'practitioner';
  practitionerCode: string;
  patients: string[];
  subscriptionStatus: 'trial' | 'active' | 'expired';
  trialEndDate: Date;
}

export interface MedicalRecord {
  patientId: string;
  diagnosis: string;
  treatmentPlan: string;
  notes: string[];
  photos: Photo[];
  appointments: Appointment[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Photo {
  id: string;
  url: string;
  type: 'clinical' | 'progress';
  date: Date;
  notes?: string;
}

export interface Appointment {
  id: string;
  patientId: string;
  practitionerId: string;
  date: Date;
  type: 'regular' | 'emergency';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

export interface ProgressData {
  patientId: string;
  dailyWear: { [date: string]: boolean };
  totalDays: number;
  completedDays: number;
  progressPercentage: number;
}

export interface ForumPost {
  id: string;
  authorId: string;
  authorName: string;
  title: string;
  content: string;
  category: string;
  isAnonymous: boolean;
  createdAt: Date;
  comments: Comment[];
  likes: number;
}

export interface Comment {
  id: string;
  authorId: string;
  authorName: string;
  content: string;
  createdAt: Date;
  isAnonymous: boolean;
}

export type Language = 'fr' | 'en';