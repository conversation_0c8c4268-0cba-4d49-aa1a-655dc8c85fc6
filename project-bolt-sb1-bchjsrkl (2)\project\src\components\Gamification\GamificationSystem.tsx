import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Trophy, Star, Target, Award, Zap, Crown } from 'lucide-react';

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  earned: boolean;
  earnedDate?: Date;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  points: number;
  progress: number;
  maxProgress: number;
  completed: boolean;
}

const GamificationSystem: React.FC = () => {
  const { user } = useAuth();
  const [userPoints, setUserPoints] = useState(0);
  const [userLevel, setUserLevel] = useState(1);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [streak, setStreak] = useState(0);

  useEffect(() => {
    loadGamificationData();
  }, [user?.id]);

  const loadGamificationData = () => {
    const saved = localStorage.getItem(`gamification_${user?.id}`);
    if (saved) {
      const data = JSON.parse(saved);
      setUserPoints(data.points || 0);
      setUserLevel(data.level || 1);
      setStreak(data.streak || 0);
    }

    // Initialize badges
    const defaultBadges: Badge[] = [
      {
        id: 'first_week',
        name: 'Première semaine',
        description: 'Portez votre appareil 7 jours consécutifs',
        icon: Star,
        color: 'text-yellow-500',
        earned: false
      },
      {
        id: 'perfect_month',
        name: 'Mois parfait',
        description: 'Portez votre appareil tous les jours du mois',
        icon: Crown,
        color: 'text-purple-500',
        earned: false
      },
      {
        id: 'photo_master',
        name: 'Photographe expert',
        description: 'Envoyez 10 photos de progression',
        icon: Award,
        color: 'text-blue-500',
        earned: false
      },
      {
        id: 'community_helper',
        name: 'Aide communautaire',
        description: 'Aidez 5 autres patients sur le forum',
        icon: Trophy,
        color: 'text-green-500',
        earned: false
      }
    ];

    setBadges(defaultBadges);

    // Initialize achievements
    const defaultAchievements: Achievement[] = [
      {
        id: 'daily_wear',
        title: 'Port quotidien',
        description: 'Portez votre appareil aujourd\'hui',
        points: 10,
        progress: 0,
        maxProgress: 1,
        completed: false
      },
      {
        id: 'weekly_consistency',
        title: 'Régularité hebdomadaire',
        description: 'Portez votre appareil 7 jours cette semaine',
        points: 100,
        progress: 0,
        maxProgress: 7,
        completed: false
      },
      {
        id: 'photo_sharing',
        title: 'Partage de progression',
        description: 'Partagez une photo de vos progrès',
        points: 50,
        progress: 0,
        maxProgress: 1,
        completed: false
      }
    ];

    setAchievements(defaultAchievements);
  };

  const addPoints = (points: number, reason: string) => {
    const newPoints = userPoints + points;
    const newLevel = Math.floor(newPoints / 1000) + 1;
    
    setUserPoints(newPoints);
    setUserLevel(newLevel);

    // Save to localStorage
    const gamificationData = {
      points: newPoints,
      level: newLevel,
      streak,
      lastUpdate: new Date()
    };
    localStorage.setItem(`gamification_${user?.id}`, JSON.stringify(gamificationData));

    // Show notification
    showAchievementNotification(`+${points} points - ${reason}`);
  };

  const showAchievementNotification = (message: string) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('OrthoProgress - Récompense!', {
        body: message,
        icon: '/WhatsApp Image 2025-06-09 at 08.39.07.jpeg'
      });
    }
  };

  const earnBadge = (badgeId: string) => {
    setBadges(prev => prev.map(badge => 
      badge.id === badgeId 
        ? { ...badge, earned: true, earnedDate: new Date() }
        : badge
    ));
    showAchievementNotification('Nouveau badge débloqué!');
  };

  const updateAchievement = (achievementId: string, progress: number) => {
    setAchievements(prev => prev.map(achievement => {
      if (achievement.id === achievementId) {
        const newProgress = Math.min(progress, achievement.maxProgress);
        const completed = newProgress >= achievement.maxProgress;
        
        if (completed && !achievement.completed) {
          addPoints(achievement.points, achievement.title);
        }
        
        return {
          ...achievement,
          progress: newProgress,
          completed
        };
      }
      return achievement;
    }));
  };

  const getProgressPercentage = (points: number) => {
    const currentLevelPoints = (userLevel - 1) * 1000;
    const nextLevelPoints = userLevel * 1000;
    return ((points - currentLevelPoints) / (nextLevelPoints - currentLevelPoints)) * 100;
  };

  return (
    <div className="space-y-6">
      {/* User Stats */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-2xl font-bold">Niveau {userLevel}</h3>
            <p className="text-blue-100">{userPoints} points</p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="h-5 w-5 text-yellow-400" />
              <span className="font-semibold">{streak} jours consécutifs</span>
            </div>
          </div>
        </div>
        
        <div className="w-full bg-blue-800 rounded-full h-3">
          <div 
            className="bg-yellow-400 h-3 rounded-full transition-all duration-500"
            style={{ width: `${getProgressPercentage(userPoints)}%` }}
          ></div>
        </div>
        <p className="text-sm text-blue-100 mt-2">
          {1000 - (userPoints % 1000)} points jusqu'au niveau {userLevel + 1}
        </p>
      </div>

      {/* Badges */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Badges</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {badges.map((badge) => (
            <div
              key={badge.id}
              className={`p-4 rounded-lg border-2 text-center transition-all ${
                badge.earned
                  ? 'border-yellow-400 bg-yellow-50'
                  : 'border-gray-200 bg-gray-50 opacity-60'
              }`}
            >
              <badge.icon className={`h-8 w-8 mx-auto mb-2 ${badge.color}`} />
              <h4 className="font-medium text-sm text-gray-900">{badge.name}</h4>
              <p className="text-xs text-gray-600 mt-1">{badge.description}</p>
              {badge.earned && badge.earnedDate && (
                <p className="text-xs text-yellow-600 mt-2">
                  Obtenu le {badge.earnedDate.toLocaleDateString()}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Achievements */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Défis en cours</h3>
        <div className="space-y-4">
          {achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`p-4 rounded-lg border ${
                achievement.completed
                  ? 'border-green-200 bg-green-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{achievement.title}</h4>
                <span className="text-sm font-semibold text-blue-600">
                  {achievement.points} pts
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{achievement.description}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        achievement.completed ? 'bg-green-500' : 'bg-blue-500'
                      }`}
                      style={{
                        width: `${(achievement.progress / achievement.maxProgress) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                <span className="text-sm text-gray-600">
                  {achievement.progress}/{achievement.maxProgress}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => {
              updateAchievement('daily_wear', 1);
              addPoints(10, 'Port quotidien');
            }}
            className="p-4 bg-blue-50 rounded-lg border border-blue-200 hover:bg-blue-100 transition-colors"
          >
            <Target className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-blue-900">Marquer aujourd'hui</p>
            <p className="text-xs text-blue-600">+10 points</p>
          </button>
          
          <button
            onClick={() => {
              updateAchievement('photo_sharing', 1);
              addPoints(50, 'Photo partagée');
            }}
            className="p-4 bg-green-50 rounded-lg border border-green-200 hover:bg-green-100 transition-colors"
          >
            <Award className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-900">Partager une photo</p>
            <p className="text-xs text-green-600">+50 points</p>
          </button>
          
          <button
            onClick={() => addPoints(25, 'Participation forum')}
            className="p-4 bg-purple-50 rounded-lg border border-purple-200 hover:bg-purple-100 transition-colors"
          >
            <Trophy className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-purple-900">Aider sur le forum</p>
            <p className="text-xs text-purple-600">+25 points</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default GamificationSystem;