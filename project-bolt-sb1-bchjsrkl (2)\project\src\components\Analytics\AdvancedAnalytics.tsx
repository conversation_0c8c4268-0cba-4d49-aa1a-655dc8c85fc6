import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts'
import { useAuth } from '../../contexts/AuthContext'
import {
  TrendingUp, TrendingDown, Target, Award, Calendar,
  Clock, Zap, Brain, Activity, BarChart3
} from 'lucide-react'

interface AnalyticsData {
  complianceRate: number
  weeklyTrend: number
  monthlyTrend: number
  currentStreak: number
  longestStreak: number
  averageHours: number
  totalDays: number
  compliantDays: number
  progressData: Array<{
    date: string
    compliance: number
    hours: number
    quality: number
  }>
  weeklyData: Array<{
    week: string
    compliance: number
    hours: number
  }>
  qualityDistribution: Array<{
    name: string
    value: number
    color: string
  }>
  predictions: {
    estimatedCompletion: string
    riskLevel: 'low' | 'medium' | 'high'
    recommendations: string[]
  }
}

const AdvancedAnalytics: React.FC = () => {
  const { user } = useAuth()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  useEffect(() => {
    if (user) {
      loadAnalyticsData()
    }
  }, [user, timeRange])

  const loadAnalyticsData = async () => {
    if (!user) return

    setLoading(true)
    try {
      // Obtenir les données de progression
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365
      
      // Simuler des données pour la démo (remplacer par de vraies données Supabase)
      const mockData = generateMockAnalyticsData(days)
      setAnalytics(mockData)
    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateMockAnalyticsData = (days: number): AnalyticsData => {
    const progressData = []
    const weeklyData = []
    let currentStreak = 0
    let longestStreak = 0
    let tempStreak = 0
    let totalHours = 0
    let compliantDays = 0

    // Générer des données de progression
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      
      const compliance = Math.random() > 0.2 ? 1 : 0 // 80% de conformité
      const hours = compliance ? 18 + Math.random() * 6 : Math.random() * 10
      const quality = compliance ? 0.7 + Math.random() * 0.3 : Math.random() * 0.5

      if (compliance) {
        tempStreak++
        compliantDays++
        if (i === 0) currentStreak = tempStreak
      } else {
        longestStreak = Math.max(longestStreak, tempStreak)
        tempStreak = 0
      }

      totalHours += hours

      progressData.push({
        date: date.toISOString().split('T')[0],
        compliance: compliance * 100,
        hours: Math.round(hours * 10) / 10,
        quality: Math.round(quality * 100)
      })
    }

    // Générer des données hebdomadaires
    for (let i = Math.ceil(days / 7) - 1; i >= 0; i--) {
      const weekStart = new Date()
      weekStart.setDate(weekStart.getDate() - (i * 7))
      
      const weekData = progressData.slice(i * 7, (i + 1) * 7)
      const avgCompliance = weekData.reduce((sum, d) => sum + d.compliance, 0) / weekData.length
      const avgHours = weekData.reduce((sum, d) => sum + d.hours, 0) / weekData.length

      weeklyData.push({
        week: `S${Math.ceil(days / 7) - i}`,
        compliance: Math.round(avgCompliance),
        hours: Math.round(avgHours * 10) / 10
      })
    }

    const complianceRate = (compliantDays / days) * 100
    const averageHours = totalHours / days

    return {
      complianceRate: Math.round(complianceRate),
      weeklyTrend: Math.random() * 20 - 10, // -10 à +10
      monthlyTrend: Math.random() * 15 - 7.5,
      currentStreak,
      longestStreak: Math.max(longestStreak, tempStreak),
      averageHours: Math.round(averageHours * 10) / 10,
      totalDays: days,
      compliantDays,
      progressData,
      weeklyData,
      qualityDistribution: [
        { name: 'Excellent', value: 35, color: '#10B981' },
        { name: 'Bon', value: 40, color: '#3B82F6' },
        { name: 'Moyen', value: 20, color: '#F59E0B' },
        { name: 'Faible', value: 5, color: '#EF4444' }
      ],
      predictions: {
        estimatedCompletion: '2025-08-15',
        riskLevel: complianceRate > 85 ? 'low' : complianceRate > 70 ? 'medium' : 'high',
        recommendations: generateRecommendations(complianceRate, currentStreak, averageHours)
      }
    }
  }

  const generateRecommendations = (compliance: number, streak: number, avgHours: number): string[] => {
    const recommendations = []

    if (compliance < 80) {
      recommendations.push('Améliorez votre régularité pour de meilleurs résultats')
    }
    if (streak === 0) {
      recommendations.push('Commencez une nouvelle série dès aujourd\'hui')
    }
    if (avgHours < 20) {
      recommendations.push('Visez 20-22 heures de port par jour')
    }
    if (compliance > 90) {
      recommendations.push('Excellent travail ! Continuez ainsi')
    }

    return recommendations
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Aucune donnée d'analyse disponible</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header avec sélecteur de période */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Avancées</h2>
        <div className="flex space-x-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                timeRange === range
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {range === '7d' ? '7 jours' : range === '30d' ? '30 jours' : range === '90d' ? '3 mois' : '1 an'}
            </button>
          ))}
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Taux de Conformité</p>
              <p className="text-3xl font-bold">{analytics.complianceRate}%</p>
              <div className="flex items-center mt-2">
                {analytics.weeklyTrend > 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-300" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-300" />
                )}
                <span className="text-sm ml-1">
                  {analytics.weeklyTrend > 0 ? '+' : ''}{analytics.weeklyTrend.toFixed(1)}% cette semaine
                </span>
              </div>
            </div>
            <Target className="h-12 w-12 text-blue-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Série Actuelle</p>
              <p className="text-3xl font-bold">{analytics.currentStreak}</p>
              <p className="text-sm text-green-100">
                Record: {analytics.longestStreak} jours
              </p>
            </div>
            <Zap className="h-12 w-12 text-green-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Heures Moyennes</p>
              <p className="text-3xl font-bold">{analytics.averageHours}h</p>
              <p className="text-sm text-purple-100">
                Objectif: 20-22h/jour
              </p>
            </div>
            <Clock className="h-12 w-12 text-purple-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm">Jours Conformes</p>
              <p className="text-3xl font-bold">{analytics.compliantDays}</p>
              <p className="text-sm text-orange-100">
                sur {analytics.totalDays} jours
              </p>
            </div>
            <Calendar className="h-12 w-12 text-orange-200" />
          </div>
        </motion.div>
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution de la conformité */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution de la Conformité</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={analytics.progressData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => new Date(date).toLocaleDateString()} />
              <YAxis />
              <Tooltip 
                labelFormatter={(date) => new Date(date).toLocaleDateString()}
                formatter={(value, name) => [
                  name === 'compliance' ? `${value}%` : `${value}h`,
                  name === 'compliance' ? 'Conformité' : 'Heures'
                ]}
              />
              <Area 
                type="monotone" 
                dataKey="compliance" 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Répartition de la qualité */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition de la Qualité</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics.qualityDistribution}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {analytics.qualityDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Prédictions IA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Brain className="h-6 w-6 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">Prédictions IA</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <p className="text-sm text-gray-600 mb-1">Fin de traitement estimée</p>
            <p className="text-xl font-bold text-indigo-600">
              {new Date(analytics.predictions.estimatedCompletion).toLocaleDateString()}
            </p>
          </div>
          
          <div>
            <p className="text-sm text-gray-600 mb-1">Niveau de risque</p>
            <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
              analytics.predictions.riskLevel === 'low' 
                ? 'bg-green-100 text-green-800'
                : analytics.predictions.riskLevel === 'medium'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {analytics.predictions.riskLevel === 'low' ? 'Faible' : 
               analytics.predictions.riskLevel === 'medium' ? 'Moyen' : 'Élevé'}
            </span>
          </div>
          
          <div>
            <p className="text-sm text-gray-600 mb-2">Recommandations</p>
            <ul className="space-y-1">
              {analytics.predictions.recommendations.map((rec, index) => (
                <li key={index} className="text-sm text-gray-700 flex items-start">
                  <span className="text-indigo-500 mr-2">•</span>
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default AdvancedAnalytics
