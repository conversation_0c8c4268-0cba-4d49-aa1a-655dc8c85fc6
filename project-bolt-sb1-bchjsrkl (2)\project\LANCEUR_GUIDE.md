# 🚀 GUIDE DES LANCEURS ORTHOPROGRESS

## 📋 Vue d'Ensemble

J'ai créé **3 lanceurs automatiques** pour votre application OrthProgress qui détectent automatiquement votre système et lancent l'application avec toutes les vérifications nécessaires.

## 📁 Fichiers Créés

### 1. `lancerpreview.bat` - Windows (Batch)
- ✅ Compatible avec toutes les versions de Windows
- ✅ Interface colorée dans l'invite de commande
- ✅ Vérifications automatiques des prérequis
- ✅ Installation automatique des dépendances

### 2. `lancerpreview.ps1` - Windows (PowerShell)
- ✅ Interface moderne et colorée
- ✅ Gestion avancée des erreurs
- ✅ Fonctionnalités PowerShell avancées
- ✅ Meilleure expérience utilisateur

### 3. `lancerpreview.sh` - Unix/Linux/Mac (Bash)
- ✅ Compatible macOS, Linux, WSL
- ✅ Détection automatique du navigateur
- ✅ Interface colorée dans le terminal
- ✅ Gestion des signaux (Ctrl+C)

## 🖥️ Comment Utiliser

### 🪟 Sur Windows

#### Option 1 : <PERSON><PERSON><PERSON> (Recommandé pour débutants)
```bash
# Double-cliquez sur le fichier
lancerpreview.bat

# Ou depuis l'invite de commande
cd "C:\Users\<USER>\Desktop\orthoprogress\project-bolt-sb1-bchjsrkl (2)\project"
lancerpreview.bat
```

#### Option 2 : PowerShell (Recommandé pour utilisateurs avancés)
```powershell
# Clic droit sur le fichier > "Exécuter avec PowerShell"
# Ou depuis PowerShell
cd "C:\Users\<USER>\Desktop\orthoprogress\project-bolt-sb1-bchjsrkl (2)\project"
.\lancerpreview.ps1
```

### 🍎 Sur macOS
```bash
# Ouvrir Terminal
cd /path/to/orthoprogress/project
chmod +x lancerpreview.sh
./lancerpreview.sh
```

### 🐧 Sur Linux
```bash
# Ouvrir Terminal
cd /path/to/orthoprogress/project
chmod +x lancerpreview.sh
./lancerpreview.sh
```

### 🔧 Sur WSL (Windows Subsystem for Linux)
```bash
cd /mnt/c/Users/<USER>/Desktop/orthoprogress/project-bolt-sb1-bchjsrkl\ \(2\)/project
chmod +x lancerpreview.sh
./lancerpreview.sh
```

## ✨ Fonctionnalités des Lanceurs

### 🔍 Vérifications Automatiques
- ✅ **Node.js** installé et version
- ✅ **npm** disponible et version
- ✅ **package.json** présent
- ✅ **Port 5173** disponible
- ✅ **Dépendances** installées

### 📦 Installation Automatique
- ✅ Détection si `node_modules` manque
- ✅ Installation automatique avec `npm install`
- ✅ Fallback avec `npm install --force` si échec
- ✅ Messages d'erreur détaillés

### 🌐 Lancement Intelligent
- ✅ Démarrage du serveur de développement
- ✅ Ouverture automatique du navigateur
- ✅ Affichage de l'URL d'accès
- ✅ Instructions d'utilisation

### 📊 Informations Complètes
- ✅ Comptes de test disponibles
- ✅ Fonctionnalités de l'application
- ✅ Conseils d'utilisation
- ✅ Informations pour développeurs

## 🎯 Utilisation Recommandée

### Pour Utilisateurs Finaux
**Utilisez `lancerpreview.bat`** - Le plus simple, double-clic et c'est parti !

### Pour Développeurs
**Utilisez `lancerpreview.ps1`** - Interface plus riche et gestion d'erreurs avancée

### Pour Systèmes Unix
**Utilisez `lancerpreview.sh`** - Optimisé pour macOS/Linux

## 🔐 Comptes de Test Intégrés

Les lanceurs affichent automatiquement les comptes de test :

```
📧 Patient: <EMAIL> / password123
👨‍⚕️ Praticien: <EMAIL> / password123
🔧 Admin: <EMAIL> / password123
```

## 🚀 Ce que Vous Verrez

### 1. Vérifications Initiales
```
🔍 Vérification des prérequis...
✅ Node.js détecté: v18.17.0
✅ npm détecté: 9.6.7
✅ Fichier package.json trouvé
```

### 2. Installation (si nécessaire)
```
📦 Installation des dépendances nécessaires...
⏳ Cela peut prendre quelques minutes...
✅ Dépendances installées avec succès!
```

### 3. Lancement
```
🌐 Lancement du serveur de développement...
📱 L'application sera disponible à l'adresse:
   👉 http://localhost:5173
🔄 Le navigateur s'ouvrira automatiquement...
```

### 4. Informations Complètes
```
🎉 ORTHOPROGRESS LANCÉ AVEC SUCCÈS!
📋 Informations de l'application:
   • Nom: OrthProgress
   • Version: 2.0.0 (Améliorée avec IA)
   • URL: http://localhost:5173
   • Mode: Développement
```

## 🛠️ Dépannage

### Problème : "Node.js n'est pas installé"
**Solution :** Installez Node.js depuis https://nodejs.org/

### Problème : "package.json non trouvé"
**Solution :** Assurez-vous d'être dans le bon répertoire du projet

### Problème : "Port 5173 déjà utilisé"
**Solution :** Le lanceur utilisera automatiquement un port alternatif

### Problème : "Erreur d'installation des dépendances"
**Solutions :**
1. Vérifiez votre connexion internet
2. Supprimez `node_modules` et `package-lock.json`
3. Relancez le script

### Problème : PowerShell bloque l'exécution
**Solution :**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🎨 Personnalisation

Vous pouvez modifier les lanceurs pour :
- Changer le port par défaut
- Ajouter des vérifications supplémentaires
- Modifier les comptes de test
- Personnaliser les messages

## 📱 Accès Mobile

Une fois lancé, l'application est accessible sur mobile via :
- **Même réseau WiFi :** `http://[IP-de-votre-PC]:5173`
- **Exemple :** `http://*************:5173`

## 🔄 Arrêt de l'Application

Pour arrêter l'application :
- **Windows :** Appuyez sur `Ctrl+C` dans la fenêtre du lanceur
- **Unix/Mac :** Appuyez sur `Ctrl+C` dans le terminal
- **Ou :** Fermez simplement la fenêtre du terminal/invite de commande

## 🎉 Prêt à Utiliser !

Vos lanceurs sont maintenant prêts ! Choisissez celui qui correspond à votre système et double-cliquez pour lancer OrthProgress automatiquement.

**Recommandation :** Créez un raccourci sur votre bureau pour un accès encore plus rapide !

---

**OrthProgress - Lancé en un clic ! 🦷✨🚀**
