import React, { useState, useRef } from 'react'
import { Camera, Upload, Zap, Eye, TrendingUp, AlertCircle, CheckCircle, Brain } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { notify } from '../../lib/notifications'

interface AnalysisResult {
  overallScore: number
  improvements: Array<{
    area: string
    score: number
    description: string
    recommendation: string
  }>
  measurements: {
    alignment: number
    spacing: number
    symmetry: number
    occlusion: number
  }
  comparison?: {
    previousPhoto: string
    progressPercentage: number
    changes: string[]
  }
  aiInsights: string[]
  nextSteps: string[]
}

const PhotoAnalysis: React.FC = () => {
  const { user } = useAuth()
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [analysisHistory, setAnalysisHistory] = useState<Array<{
    id: string
    date: Date
    image: string
    result: AnalysisResult
  }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        setSelectedImage(imageUrl)
        analyzeImage(imageUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  const analyzeImage = async (imageUrl: string) => {
    setIsAnalyzing(true)
    setAnalysisResult(null)

    try {
      // Simuler l'analyse IA (remplacer par une vraie API)
      await new Promise(resolve => setTimeout(resolve, 3000))

      const mockResult: AnalysisResult = {
        overallScore: 78,
        improvements: [
          {
            area: 'Alignement des incisives',
            score: 85,
            description: 'Amélioration significative de l\'alignement des dents de devant',
            recommendation: 'Continuez le port régulier pour maintenir les progrès'
          },
          {
            area: 'Espacement interdentaire',
            score: 72,
            description: 'Réduction notable des espaces entre les dents',
            recommendation: 'Utilisez le fil dentaire quotidiennement'
          },
          {
            area: 'Symétrie du sourire',
            score: 80,
            description: 'Amélioration de la symétrie générale',
            recommendation: 'Exercices de sourire recommandés'
          },
          {
            area: 'Occlusion',
            score: 65,
            description: 'Progrès modéré dans l\'ajustement de la morsure',
            recommendation: 'Attention particulière au port nocturne'
          }
        ],
        measurements: {
          alignment: 85,
          spacing: 72,
          symmetry: 80,
          occlusion: 65
        },
        comparison: {
          previousPhoto: '/api/placeholder/200/150',
          progressPercentage: 23,
          changes: [
            'Réduction de 2.3mm de l\'encombrement antérieur',
            'Amélioration de 15° de l\'angulation des canines',
            'Fermeture partielle de l\'espace entre les incisives centrales'
          ]
        },
        aiInsights: [
          'Vos progrès sont excellents pour cette étape du traitement',
          'La régularité du port se reflète dans l\'amélioration constante',
          'Attention à maintenir une hygiène optimale pendant cette phase',
          'Les résultats suggèrent une fin de traitement dans 4-6 mois'
        ],
        nextSteps: [
          'Continuer le port 20-22h par jour',
          'Prendre une nouvelle photo dans 2 semaines',
          'Planifier un contrôle avec votre praticien',
          'Maintenir une hygiène dentaire rigoureuse'
        ]
      }

      setAnalysisResult(mockResult)
      
      // Sauvegarder dans l'historique
      const newAnalysis = {
        id: Date.now().toString(),
        date: new Date(),
        image: imageUrl,
        result: mockResult
      }
      setAnalysisHistory(prev => [newAnalysis, ...prev])

      notify.success('Analyse terminée avec succès !')
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error)
      notify.error('Erreur lors de l\'analyse de l\'image')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (score >= 60) return <AlertCircle className="h-5 w-5 text-yellow-600" />
    return <AlertCircle className="h-5 w-5 text-red-600" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white">
        <div className="flex items-center space-x-3 mb-4">
          <Brain className="h-8 w-8" />
          <h2 className="text-2xl font-bold">Analyse IA des Photos</h2>
        </div>
        <p className="text-purple-100">
          Utilisez l'intelligence artificielle pour analyser vos progrès orthodontiques
        </p>
      </div>

      {/* Upload Section */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Nouvelle Analyse</h3>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          {selectedImage ? (
            <div className="space-y-4">
              <img 
                src={selectedImage} 
                alt="Photo à analyser" 
                className="max-w-md mx-auto rounded-lg shadow-md"
              />
              {!isAnalyzing && (
                <button
                  onClick={() => {
                    setSelectedImage(null)
                    setAnalysisResult(null)
                  }}
                  className="text-red-600 hover:text-red-700 font-medium"
                >
                  Changer d'image
                </button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <Camera className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Sélectionner une photo
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <p className="text-gray-500 text-sm mt-2">
                  Formats acceptés: JPG, PNG, HEIC
                </p>
              </div>
            </div>
          )}
        </div>

        {isAnalyzing && (
          <div className="mt-6 text-center">
            <div className="inline-flex items-center space-x-3 bg-blue-50 px-6 py-4 rounded-lg">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-blue-800 font-medium">Analyse en cours...</span>
            </div>
            <p className="text-gray-600 text-sm mt-2">
              L'IA analyse votre photo pour détecter les progrès
            </p>
          </div>
        )}
      </div>

      {/* Résultats d'analyse */}
      {analysisResult && (
        <div className="space-y-6">
          {/* Score global */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Score Global</h3>
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                <span className="text-sm text-gray-600">Analyse IA</span>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {analysisResult.overallScore}/100
              </div>
              <p className="text-gray-600">
                {analysisResult.overallScore >= 80 ? 'Excellents progrès !' :
                 analysisResult.overallScore >= 60 ? 'Bons progrès' : 'Progrès à améliorer'}
              </p>
            </div>
          </div>

          {/* Détails par zone */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Analyse Détaillée</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analysisResult.improvements.map((improvement, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{improvement.area}</h4>
                    <div className="flex items-center space-x-2">
                      {getScoreIcon(improvement.score)}
                      <span className={`px-2 py-1 rounded-full text-sm font-medium ${getScoreColor(improvement.score)}`}>
                        {improvement.score}/100
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{improvement.description}</p>
                  <p className="text-sm text-blue-600 font-medium">{improvement.recommendation}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Comparaison avec photo précédente */}
          {analysisResult.comparison && (
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Comparaison des Progrès</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Changements Détectés</h4>
                  <ul className="space-y-2">
                    {analysisResult.comparison.changes.map((change, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <TrendingUp className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{change}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    +{analysisResult.comparison.progressPercentage}%
                  </div>
                  <p className="text-gray-600">Amélioration globale</p>
                </div>
              </div>
            </div>
          )}

          {/* Insights IA */}
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Eye className="h-5 w-5 text-indigo-600 mr-2" />
              Insights IA
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Observations</h4>
                <ul className="space-y-2">
                  {analysisResult.aiInsights.map((insight, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-indigo-500 mr-2">•</span>
                      {insight}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Prochaines Étapes</h4>
                <ul className="space-y-2">
                  {analysisResult.nextSteps.map((step, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-purple-500 mr-2">→</span>
                      {step}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Historique des analyses */}
      {analysisHistory.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Historique des Analyses</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {analysisHistory.slice(0, 6).map((analysis) => (
              <div key={analysis.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <img 
                  src={analysis.image} 
                  alt="Analyse précédente" 
                  className="w-full h-32 object-cover rounded-lg mb-3"
                />
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {analysis.date.toLocaleDateString()}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(analysis.result.overallScore)}`}>
                    {analysis.result.overallScore}/100
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default PhotoAnalysis
