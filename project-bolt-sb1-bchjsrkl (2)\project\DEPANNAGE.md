# 🛠️ GUIDE DE DÉPANNAGE ORTHOPROGRESS

## ❌ **PROBLÈME PRINCIPAL IDENTIFIÉ**

**Node.js n'est pas installé sur votre système !**

C'est la cause principale qui empêche le lancement de l'application.

## 🚀 **SOLUTIONS RAPIDES**

### **SOLUTION 1 : Installation Automatique (Recommandée)**

1. **Double-cliquez** sur le fichier : `installer-nodejs.bat`
2. **Suivez les instructions** à l'écran
3. **Redémarrez** votre ordinateur si demandé
4. **Lancez** ensuite `lancerpreview.bat`

### **SOLUTION 2 : Installation Manuelle**

1. **Allez sur** : https://nodejs.org/
2. **Téléchargez** la version LTS (Long Term Support)
3. **Exécutez** l'installateur téléchargé
4. **Acceptez** tous les paramètres par défaut
5. **<PERSON><PERSON><PERSON>rez** votre ordinateur
6. **Testez** en ouvrant PowerShell et tapant : `node --version`

## 🔍 **DIAGNOSTIC COMPLET**

### **Vérifier si Node.js est installé :**
```powershell
# Ouvrir PowerShell et taper :
node --version
npm --version
```

**Résultat attendu :**
```
v18.17.0
9.6.7
```

**Si vous voyez une erreur :** Node.js n'est pas installé

### **Vérifier le PATH :**
```powershell
echo $env:PATH
```
Vous devriez voir quelque chose contenant `nodejs` ou `npm`

## 🛠️ **PROBLÈMES COURANTS ET SOLUTIONS**

### **1. "Le terme 'node' n'est pas reconnu"**
**Cause :** Node.js pas installé ou pas dans le PATH
**Solution :** Installer Node.js avec `installer-nodejs.bat`

### **2. "npm install échoue"**
**Causes possibles :**
- Connexion internet lente/coupée
- Antivirus bloquant
- Permissions insuffisantes

**Solutions :**
```powershell
# Nettoyer le cache npm
npm cache clean --force

# Installer avec force
npm install --force

# Ou avec legacy peer deps
npm install --legacy-peer-deps
```

### **3. "Port 5173 déjà utilisé"**
**Solution :** Le lanceur gère automatiquement ce cas
**Alternative manuelle :**
```powershell
npm run dev -- --port 3000
```

### **4. "Permission denied" ou erreurs de permissions**
**Solutions :**
- Exécuter PowerShell en tant qu'administrateur
- Ou changer les permissions npm :
```powershell
npm config set prefix %APPDATA%\npm
```

### **5. "Module not found" après installation**
**Solutions :**
```powershell
# Supprimer node_modules et réinstaller
rmdir /s node_modules
del package-lock.json
npm install
```

## 📋 **CHECKLIST DE VÉRIFICATION**

Avant de lancer l'application, vérifiez :

- [ ] ✅ Node.js installé (`node --version` fonctionne)
- [ ] ✅ npm installé (`npm --version` fonctionne)
- [ ] ✅ Dans le bon répertoire (contient `package.json`)
- [ ] ✅ Connexion internet active
- [ ] ✅ Antivirus ne bloque pas npm
- [ ] ✅ Permissions suffisantes

## 🔄 **PROCÉDURE DE LANCEMENT ÉTAPE PAR ÉTAPE**

### **Étape 1 : Vérification**
```powershell
# Ouvrir PowerShell dans le dossier du projet
cd "C:\Users\<USER>\Desktop\orthoprogress\project-bolt-sb1-bchjsrkl (2)\project"

# Vérifier Node.js
node --version
npm --version
```

### **Étape 2 : Installation des dépendances**
```powershell
# Si node_modules n'existe pas
npm install

# En cas d'erreur
npm install --force
```

### **Étape 3 : Lancement**
```powershell
# Lancer l'application
npm run dev

# Ou utiliser le lanceur
.\lancerpreview.bat
```

## 🌐 **MÉTHODES ALTERNATIVES**

### **Méthode 1 : Via VS Code**
1. Installer VS Code
2. Ouvrir le dossier du projet
3. Terminal intégré : `Ctrl + `` 
4. Taper : `npm run dev`

### **Méthode 2 : Via Git Bash (si installé)**
```bash
cd /c/Users/<USER>/Desktop/orthoprogress/project-bolt-sb1-bchjsrkl\ \(2\)/project
npm run dev
```

### **Méthode 3 : Via WSL (Windows Subsystem for Linux)**
```bash
cd /mnt/c/Users/<USER>/Desktop/orthoprogress/project-bolt-sb1-bchjsrkl\ \(2\)/project
npm run dev
```

## 🆘 **EN CAS D'ÉCHEC TOTAL**

### **Option 1 : Réinstallation complète**
1. Désinstaller Node.js (Panneau de configuration)
2. Redémarrer l'ordinateur
3. Réinstaller Node.js depuis nodejs.org
4. Redémarrer à nouveau
5. Relancer `lancerpreview.bat`

### **Option 2 : Utiliser un environnement en ligne**
- CodeSandbox : https://codesandbox.io/
- StackBlitz : https://stackblitz.com/
- Gitpod : https://gitpod.io/

### **Option 3 : Docker (pour utilisateurs avancés)**
```dockerfile
# Créer un Dockerfile
FROM node:18
WORKDIR /app
COPY . .
RUN npm install
EXPOSE 5173
CMD ["npm", "run", "dev"]
```

## 📞 **SUPPORT SUPPLÉMENTAIRE**

### **Logs utiles à fournir :**
```powershell
# Version du système
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

# Version Node.js
node --version
npm --version

# Contenu du dossier
dir

# Erreurs npm
npm run dev 2>&1 | tee error.log
```

### **Informations système :**
- Version de Windows
- Architecture (32/64 bits)
- Antivirus utilisé
- Proxy d'entreprise (si applicable)

## 🎯 **RÉSOLUTION GARANTIE**

**Si vous suivez ces étapes dans l'ordre :**

1. ✅ Installer Node.js avec `installer-nodejs.bat`
2. ✅ Redémarrer l'ordinateur
3. ✅ Lancer `lancerpreview.bat`
4. ✅ Profiter d'OrthProgress !

**Votre application devrait fonctionner à 100% !** 🚀

## 📱 **ACCÈS RAPIDE APRÈS RÉSOLUTION**

Une fois Node.js installé, l'application sera accessible à :
- **URL locale :** http://localhost:5173
- **Comptes de test :**
  - Patient : `<EMAIL> / password123`
  - Praticien : `<EMAIL> / password123`
  - Admin : `<EMAIL> / password123`

---

**OrthProgress sera bientôt opérationnel ! 🦷✨**
