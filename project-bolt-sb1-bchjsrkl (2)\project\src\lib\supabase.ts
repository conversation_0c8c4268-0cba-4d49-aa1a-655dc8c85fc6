import { createClient } from '@supabase/supabase-js'

// Configuration Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'patient' | 'practitioner' | 'admin'
          practitioner_code: string | null
          is_vip: boolean
          subscription_status: 'trial' | 'active' | 'expired' | null
          trial_end_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role: 'patient' | 'practitioner' | 'admin'
          practitioner_code?: string | null
          is_vip?: boolean
          subscription_status?: 'trial' | 'active' | 'expired' | null
          trial_end_date?: string | null
        }
        Update: {
          email?: string
          full_name?: string
          role?: 'patient' | 'practitioner' | 'admin'
          practitioner_code?: string | null
          is_vip?: boolean
          subscription_status?: 'trial' | 'active' | 'expired' | null
          trial_end_date?: string | null
        }
      }
      patients: {
        Row: {
          id: string
          user_id: string
          practitioner_id: string | null
          treatment_start_date: string | null
          estimated_end_date: string | null
          current_aligner: number
          total_aligners: number | null
          created_at: string
        }
        Insert: {
          user_id: string
          practitioner_id?: string | null
          treatment_start_date?: string | null
          estimated_end_date?: string | null
          current_aligner?: number
          total_aligners?: number | null
        }
        Update: {
          practitioner_id?: string | null
          treatment_start_date?: string | null
          estimated_end_date?: string | null
          current_aligner?: number
          total_aligners?: number | null
        }
      }
      progress_entries: {
        Row: {
          id: string
          patient_id: string
          date: string
          worn: boolean
          hours_worn: number | null
          quality: 'excellent' | 'good' | 'fair' | 'poor' | null
          notes: string | null
          created_at: string
        }
        Insert: {
          patient_id: string
          date: string
          worn: boolean
          hours_worn?: number | null
          quality?: 'excellent' | 'good' | 'fair' | 'poor' | null
          notes?: string | null
        }
        Update: {
          worn?: boolean
          hours_worn?: number | null
          quality?: 'excellent' | 'good' | 'fair' | 'poor' | null
          notes?: string | null
        }
      }
      photos: {
        Row: {
          id: string
          patient_id: string
          url: string
          type: 'clinical' | 'progress' | 'before' | 'after'
          date: string
          notes: string | null
          ai_analysis: any | null
          created_at: string
        }
        Insert: {
          patient_id: string
          url: string
          type: 'clinical' | 'progress' | 'before' | 'after'
          date: string
          notes?: string | null
          ai_analysis?: any | null
        }
        Update: {
          url?: string
          type?: 'clinical' | 'progress' | 'before' | 'after'
          date?: string
          notes?: string | null
          ai_analysis?: any | null
        }
      }
      appointments: {
        Row: {
          id: string
          patient_id: string
          practitioner_id: string
          scheduled_date: string
          type: 'regular' | 'emergency' | 'teleconsultation'
          status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          notes: string | null
          meeting_url: string | null
          created_at: string
        }
        Insert: {
          patient_id: string
          practitioner_id: string
          scheduled_date: string
          type: 'regular' | 'emergency' | 'teleconsultation'
          status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          notes?: string | null
          meeting_url?: string | null
        }
        Update: {
          scheduled_date?: string
          type?: 'regular' | 'emergency' | 'teleconsultation'
          status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          notes?: string | null
          meeting_url?: string | null
        }
      }
      user_achievements: {
        Row: {
          id: string
          user_id: string
          achievement_type: string
          points: number
          earned_date: string
          description: string | null
        }
        Insert: {
          user_id: string
          achievement_type: string
          points: number
          description?: string | null
        }
        Update: {
          achievement_type?: string
          points?: number
          description?: string | null
        }
      }
      forum_posts: {
        Row: {
          id: string
          author_id: string
          title: string
          content: string
          category: string
          is_anonymous: boolean
          likes_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          author_id: string
          title: string
          content: string
          category: string
          is_anonymous?: boolean
        }
        Update: {
          title?: string
          content?: string
          category?: string
          is_anonymous?: boolean
        }
      }
      forum_comments: {
        Row: {
          id: string
          post_id: string
          author_id: string
          content: string
          is_anonymous: boolean
          created_at: string
        }
        Insert: {
          post_id: string
          author_id: string
          content: string
          is_anonymous?: boolean
        }
        Update: {
          content?: string
          is_anonymous?: boolean
        }
      }
      reminders: {
        Row: {
          id: string
          user_id: string
          type: 'wear' | 'hygiene' | 'appointment' | 'photo' | 'custom'
          title: string
          message: string
          time: string
          frequency: 'daily' | 'weekly' | 'monthly' | 'custom'
          enabled: boolean
          smart: boolean
          last_triggered: string | null
          created_at: string
        }
        Insert: {
          user_id: string
          type: 'wear' | 'hygiene' | 'appointment' | 'photo' | 'custom'
          title: string
          message: string
          time: string
          frequency: 'daily' | 'weekly' | 'monthly' | 'custom'
          enabled?: boolean
          smart?: boolean
          last_triggered?: string | null
        }
        Update: {
          type?: 'wear' | 'hygiene' | 'appointment' | 'photo' | 'custom'
          title?: string
          message?: string
          time?: string
          frequency?: 'daily' | 'weekly' | 'monthly' | 'custom'
          enabled?: boolean
          smart?: boolean
          last_triggered?: string | null
        }
      }
    }
    Functions: {
      calculate_compliance_rate: {
        Args: {
          patient_uuid: string
          days_back?: number
        }
        Returns: number
      }
    }
  }
}

// Helper functions pour les requêtes courantes
export const supabaseHelpers = {
  // Obtenir le profil utilisateur
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    return { data, error }
  },

  // Créer un profil utilisateur
  async createProfile(profile: Database['public']['Tables']['profiles']['Insert']) {
    const { data, error } = await supabase
      .from('profiles')
      .insert(profile)
      .select()
      .single()
    
    return { data, error }
  },

  // Obtenir les données de progression d'un patient
  async getProgressData(patientId: string, limit = 30) {
    const { data, error } = await supabase
      .from('progress_entries')
      .select('*')
      .eq('patient_id', patientId)
      .order('date', { ascending: false })
      .limit(limit)
    
    return { data, error }
  },

  // Ajouter une entrée de progression
  async addProgressEntry(entry: Database['public']['Tables']['progress_entries']['Insert']) {
    const { data, error } = await supabase
      .from('progress_entries')
      .insert(entry)
      .select()
      .single()
    
    return { data, error }
  },

  // Obtenir les photos d'un patient
  async getPatientPhotos(patientId: string) {
    const { data, error } = await supabase
      .from('photos')
      .select('*')
      .eq('patient_id', patientId)
      .order('date', { ascending: false })
    
    return { data, error }
  },

  // Calculer le taux de conformité
  async getComplianceRate(patientId: string, daysBack = 30) {
    const { data, error } = await supabase
      .rpc('calculate_compliance_rate', {
        patient_uuid: patientId,
        days_back: daysBack
      })
    
    return { data, error }
  }
}
