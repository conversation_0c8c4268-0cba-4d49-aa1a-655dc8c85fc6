import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Camera, Upload, X, Image } from 'lucide-react';

const PhotoUpload: React.FC = () => {
  const { user } = useAuth();
  const [photos, setPhotos] = useState<Array<{ id: string; url: string; date: Date; notes: string }>>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [notes, setNotes] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleUpload = () => {
    if (selectedFile && previewUrl) {
      const newPhoto = {
        id: Date.now().toString(),
        url: previewUrl,
        date: new Date(),
        notes: notes
      };
      
      const updatedPhotos = [...photos, newPhoto];
      setPhotos(updatedPhotos);
      
      // Save to localStorage
      localStorage.setItem(`photos_${user?.id}`, JSON.stringify(updatedPhotos));
      
      // Reset form
      setSelectedFile(null);
      setPreviewUrl(null);
      setNotes('');
    }
  };

  const removePhoto = (id: string) => {
    const updatedPhotos = photos.filter(photo => photo.id !== id);
    setPhotos(updatedPhotos);
    localStorage.setItem(`photos_${user?.id}`, JSON.stringify(updatedPhotos));
  };

  React.useEffect(() => {
    const saved = localStorage.getItem(`photos_${user?.id}`);
    if (saved) {
      setPhotos(JSON.parse(saved));
    }
  }, [user?.id]);

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Envoyer des photos</h2>
        
        {/* Upload Section */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          {previewUrl ? (
            <div className="space-y-4">
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="max-w-xs mx-auto rounded-lg shadow-md"
              />
              <button
                onClick={() => {
                  setPreviewUrl(null);
                  setSelectedFile(null);
                }}
                className="text-red-600 hover:text-red-700 font-medium"
              >
                Supprimer
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <Camera className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <label htmlFor="photo-upload" className="cursor-pointer">
                  <span className="text-blue-600 hover:text-blue-700 font-medium">
                    Cliquez pour sélectionner une photo
                  </span>
                  <input
                    id="photo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </label>
                <p className="text-gray-500 text-sm mt-1">ou glissez-déposez une image</p>
              </div>
            </div>
          )}
        </div>

        {/* Notes Section */}
        {selectedFile && (
          <div className="mt-6 space-y-4">
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes (optionnel)
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ajoutez des notes sur cette photo..."
              />
            </div>
            
            <button
              onClick={handleUpload}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center space-x-2"
            >
              <Upload className="h-5 w-5" />
              <span>Envoyer la photo</span>
            </button>
          </div>
        )}
      </div>

      {/* Photos Gallery */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Photos envoyées</h3>
        
        {photos.length === 0 ? (
          <div className="text-center py-8">
            <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Aucune photo envoyée pour le moment</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {photos.map((photo) => (
              <div key={photo.id} className="relative group">
                <img
                  src={photo.url}
                  alt="Photo clinique"
                  className="w-full h-48 object-cover rounded-lg shadow-sm"
                />
                <button
                  onClick={() => removePhoto(photo.id)}
                  className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-4 w-4" />
                </button>
                <div className="mt-2">
                  <p className="text-sm text-gray-600">
                    {photo.date.toLocaleDateString()}
                  </p>
                  {photo.notes && (
                    <p className="text-sm text-gray-800 mt-1">{photo.notes}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PhotoUpload;