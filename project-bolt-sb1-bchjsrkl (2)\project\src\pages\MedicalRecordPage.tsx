import React from 'react';
import { FileText, Calendar, Camera, User } from 'lucide-react';

const MedicalRecordPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Dossier médical</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
              <User className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium text-blue-900">Informations patient</h3>
                <p className="text-sm text-blue-700">Données personnelles et contact</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
              <FileText className="h-5 w-5 text-green-600" />
              <div>
                <h3 className="font-medium text-green-900">Plan de traitement</h3>
                <p className="text-sm text-green-700">Objectifs et étapes du traitement</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <h3 className="font-medium text-purple-900">Historique des visites</h3>
                <p className="text-sm text-purple-700">Consultations et ajustements</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-4 bg-orange-50 rounded-lg">
              <Camera className="h-5 w-5 text-orange-600" />
              <div>
                <h3 className="font-medium text-orange-900">Photos cliniques</h3>
                <p className="text-sm text-orange-700">Évolution du traitement</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-4">Notes du praticien</h3>
          <div className="space-y-3">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex justify-between items-start mb-2">
                <span className="font-medium text-gray-900">Consultation du 15/12/2024</span>
                <span className="text-sm text-gray-500">Dr. Martin</span>
              </div>
              <p className="text-gray-700 text-sm">
                Bonne progression du traitement. Port de l'appareil respecté à 85%. 
                Ajustement mineur effectué. Prochain RDV dans 4 semaines.
              </p>
            </div>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex justify-between items-start mb-2">
                <span className="font-medium text-gray-900">Consultation du 17/11/2024</span>
                <span className="text-sm text-gray-500">Dr. Martin</span>
              </div>
              <p className="text-gray-700 text-sm">
                Début du traitement. Explications données au patient sur le port de l'appareil. 
                Première série d'aligneurs remise.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalRecordPage;