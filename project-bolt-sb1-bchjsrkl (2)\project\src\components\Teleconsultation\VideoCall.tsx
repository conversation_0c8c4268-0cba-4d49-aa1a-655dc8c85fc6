import React, { useState, useEffect, useRef } from 'react'
import { 
  <PERSON>, VideoOff, Mic, MicOff, Phone, PhoneOff, 
  Monitor, Camera, Settings, MessageSquare, Users,
  Record, StopCircle, Share, Download
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { notify } from '../../lib/notifications'

interface VideoCallProps {
  appointmentId: string
  isHost: boolean
  onCallEnd: () => void
}

interface CallState {
  isConnected: boolean
  isVideoEnabled: boolean
  isAudioEnabled: boolean
  isScreenSharing: boolean
  isRecording: boolean
  participants: Array<{
    id: string
    name: string
    isHost: boolean
    videoEnabled: boolean
    audioEnabled: boolean
  }>
  messages: Array<{
    id: string
    senderId: string
    senderName: string
    message: string
    timestamp: Date
  }>
}

const VideoCall: React.FC<VideoCallProps> = ({ appointmentId, isHost, onCallEnd }) => {
  const { user } = useAuth()
  const [callState, setCallState] = useState<CallState>({
    isConnected: false,
    isVideoEnabled: true,
    isAudioEnabled: true,
    isScreenSharing: false,
    isRecording: false,
    participants: [],
    messages: []
  })
  const [newMessage, setNewMessage] = useState('')
  const [showChat, setShowChat] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)
  const localStreamRef = useRef<MediaStream | null>(null)
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null)

  useEffect(() => {
    initializeCall()
    return () => {
      cleanup()
    }
  }, [])

  const initializeCall = async () => {
    try {
      // Obtenir l'accès à la caméra et au microphone
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })

      localStreamRef.current = stream
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream
      }

      // Initialiser la connexion WebRTC
      await setupPeerConnection()
      
      setCallState(prev => ({ ...prev, isConnected: true }))
      notify.success('Connexion établie avec succès')
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de l\'appel:', error)
      notify.error('Impossible d\'accéder à la caméra ou au microphone')
    }
  }

  const setupPeerConnection = async () => {
    const configuration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        // Ajouter des serveurs TURN pour la production
      ]
    }

    const peerConnection = new RTCPeerConnection(configuration)
    peerConnectionRef.current = peerConnection

    // Ajouter le stream local
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => {
        peerConnection.addTrack(track, localStreamRef.current!)
      })
    }

    // Gérer les streams distants
    peerConnection.ontrack = (event) => {
      if (remoteVideoRef.current) {
        remoteVideoRef.current.srcObject = event.streams[0]
      }
    }

    // Gérer les candidats ICE
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        // Envoyer le candidat via WebSocket ou Supabase realtime
        console.log('ICE candidate:', event.candidate)
      }
    }

    // Gérer les changements de connexion
    peerConnection.onconnectionstatechange = () => {
      console.log('Connection state:', peerConnection.connectionState)
      if (peerConnection.connectionState === 'connected') {
        notify.success('Connexion vidéo établie')
      } else if (peerConnection.connectionState === 'disconnected') {
        notify.warning('Connexion interrompue')
      }
    }
  }

  const toggleVideo = async () => {
    if (localStreamRef.current) {
      const videoTrack = localStreamRef.current.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !callState.isVideoEnabled
        setCallState(prev => ({ ...prev, isVideoEnabled: !prev.isVideoEnabled }))
      }
    }
  }

  const toggleAudio = async () => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !callState.isAudioEnabled
        setCallState(prev => ({ ...prev, isAudioEnabled: !prev.isAudioEnabled }))
      }
    }
  }

  const startScreenShare = async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      })

      // Remplacer la piste vidéo par le partage d'écran
      const videoTrack = screenStream.getVideoTracks()[0]
      const sender = peerConnectionRef.current?.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      )

      if (sender) {
        await sender.replaceTrack(videoTrack)
      }

      setCallState(prev => ({ ...prev, isScreenSharing: true }))
      notify.success('Partage d\'écran activé')

      // Gérer l'arrêt du partage d'écran
      videoTrack.onended = () => {
        stopScreenShare()
      }
    } catch (error) {
      console.error('Erreur lors du partage d\'écran:', error)
      notify.error('Impossible de partager l\'écran')
    }
  }

  const stopScreenShare = async () => {
    try {
      // Revenir à la caméra
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      const videoTrack = stream.getVideoTracks()[0]
      
      const sender = peerConnectionRef.current?.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      )

      if (sender) {
        await sender.replaceTrack(videoTrack)
      }

      setCallState(prev => ({ ...prev, isScreenSharing: false }))
      notify.info('Partage d\'écran arrêté')
    } catch (error) {
      console.error('Erreur lors de l\'arrêt du partage d\'écran:', error)
    }
  }

  const startRecording = () => {
    // Implémenter l'enregistrement avec MediaRecorder API
    setCallState(prev => ({ ...prev, isRecording: true }))
    notify.success('Enregistrement démarré')
  }

  const stopRecording = () => {
    setCallState(prev => ({ ...prev, isRecording: false }))
    notify.success('Enregistrement arrêté et sauvegardé')
  }

  const sendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now().toString(),
        senderId: user?.id || '',
        senderName: user?.fullName || 'Utilisateur',
        message: newMessage,
        timestamp: new Date()
      }

      setCallState(prev => ({
        ...prev,
        messages: [...prev.messages, message]
      }))
      setNewMessage('')
    }
  }

  const endCall = () => {
    cleanup()
    onCallEnd()
    notify.info('Appel terminé')
  }

  const cleanup = () => {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop())
    }
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close()
    }
  }

  return (
    <div className="h-screen bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-white font-semibold">Téléconsultation</h2>
          <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs">
            En cours
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowChat(!showChat)}
            className="p-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600"
          >
            <MessageSquare className="h-5 w-5" />
          </button>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600"
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Vidéos */}
      <div className="flex-1 relative">
        {/* Vidéo principale (remote) */}
        <video
          ref={remoteVideoRef}
          autoPlay
          playsInline
          className="w-full h-full object-cover"
        />

        {/* Vidéo locale (picture-in-picture) */}
        <div className="absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden">
          <video
            ref={localVideoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-full object-cover"
          />
        </div>

        {/* Chat (si ouvert) */}
        {showChat && (
          <div className="absolute right-4 top-4 bottom-20 w-80 bg-white rounded-lg shadow-lg flex flex-col">
            <div className="p-4 border-b">
              <h3 className="font-semibold">Chat</h3>
            </div>
            <div className="flex-1 p-4 overflow-y-auto space-y-2">
              {callState.messages.map(msg => (
                <div key={msg.id} className="bg-gray-100 rounded-lg p-2">
                  <div className="text-sm font-medium">{msg.senderName}</div>
                  <div className="text-sm">{msg.message}</div>
                  <div className="text-xs text-gray-500">
                    {msg.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
            <div className="p-4 border-t flex space-x-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Tapez votre message..."
                className="flex-1 px-3 py-2 border rounded-lg"
              />
              <button
                onClick={sendMessage}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Envoyer
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Contrôles */}
      <div className="bg-gray-800 p-4">
        <div className="flex items-center justify-center space-x-4">
          <button
            onClick={toggleVideo}
            className={`p-3 rounded-full ${
              callState.isVideoEnabled 
                ? 'bg-gray-700 text-white hover:bg-gray-600' 
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {callState.isVideoEnabled ? <Video className="h-6 w-6" /> : <VideoOff className="h-6 w-6" />}
          </button>

          <button
            onClick={toggleAudio}
            className={`p-3 rounded-full ${
              callState.isAudioEnabled 
                ? 'bg-gray-700 text-white hover:bg-gray-600' 
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {callState.isAudioEnabled ? <Mic className="h-6 w-6" /> : <MicOff className="h-6 w-6" />}
          </button>

          <button
            onClick={callState.isScreenSharing ? stopScreenShare : startScreenShare}
            className={`p-3 rounded-full ${
              callState.isScreenSharing 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-700 text-white hover:bg-gray-600'
            }`}
          >
            <Monitor className="h-6 w-6" />
          </button>

          {isHost && (
            <button
              onClick={callState.isRecording ? stopRecording : startRecording}
              className={`p-3 rounded-full ${
                callState.isRecording 
                  ? 'bg-red-600 text-white hover:bg-red-700' 
                  : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
            >
              {callState.isRecording ? <StopCircle className="h-6 w-6" /> : <Record className="h-6 w-6" />}
            </button>
          )}

          <button
            onClick={endCall}
            className="p-3 bg-red-600 text-white rounded-full hover:bg-red-700"
          >
            <PhoneOff className="h-6 w-6" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default VideoCall
