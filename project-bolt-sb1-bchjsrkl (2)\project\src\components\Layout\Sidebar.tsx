import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  LayoutDashboard, 
  Users, 
  Calendar, 
  MessageSquare, 
  User, 
  CreditCard,
  Settings,
  Trophy,
  Bell,
  BarChart3,
  BookOpen,
  Camera,
  FileText,
  Video
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const patientNavItems = [
    { to: '/dashboard', icon: LayoutDashboard, label: t('nav.dashboard') },
    { to: '/progress', icon: Calendar, label: 'Suivi' },
    { to: '/photos', icon: Camera, label: 'Photos' },
    { to: '/medical-record', icon: FileText, label: 'Dossier médical' },
    { to: '/teleconsultation', icon: Video, label: 'Téléconsultation' },
    { to: '/gamification', icon: Trophy, label: 'Récompenses' },
    { to: '/reminders', icon: Bell, label: 'Rappels' },
    { to: '/analytics', icon: BarChart3, label: 'Analyses' },
    { to: '/education', icon: BookOpen, label: 'Éducation' },
    { to: '/forum', icon: MessageSquare, label: t('nav.forum') },
  ];

  const practitionerNavItems = [
    { to: '/dashboard', icon: LayoutDashboard, label: t('nav.dashboard') },
    { to: '/patients', icon: Users, label: t('nav.patients') },
    { to: '/appointments', icon: Calendar, label: t('nav.appointments') },
    { to: '/analytics', icon: BarChart3, label: 'Analyses' },
    { to: '/forum', icon: MessageSquare, label: t('nav.forum') },
    { to: '/subscription', icon: CreditCard, label: t('nav.subscription') },
  ];

  const adminNavItems = [
    { to: '/admin', icon: Settings, label: 'Administration' },
    { to: '/admin/vip', icon: Users, label: 'Comptes VIP' },
  ];

  const getNavItems = () => {
    if (user?.role === 'admin') return adminNavItems;
    if (user?.role === 'practitioner') return practitionerNavItems;
    return patientNavItems;
  };

  return (
    <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
      <nav className="mt-8 px-4">
        <ul className="space-y-2">
          {getNavItems().map((item) => (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) =>
                  `flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }`
                }
              >
                <item.icon className="h-5 w-5" />
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;