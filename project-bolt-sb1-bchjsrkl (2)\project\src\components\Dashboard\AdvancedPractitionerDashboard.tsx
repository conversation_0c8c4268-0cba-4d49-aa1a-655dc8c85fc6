import React, { useState, useEffect } from 'react'
import { 
  Users, Calendar, TrendingUp, AlertTriangle, 
  Clock, CheckCircle, XCircle, Phone, Video,
  BarChart3, FileText, Settings, Bell, Search,
  Filter, Download, Plus, Eye
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts'
import { notify } from '../../lib/notifications'

interface Patient {
  id: string
  name: string
  email: string
  treatmentProgress: number
  complianceRate: number
  lastActivity: Date
  riskLevel: 'low' | 'medium' | 'high'
  nextAppointment?: Date
  currentAligner: number
  totalAligners: number
  avatar?: string
  phone: string
  treatmentType: string
  startDate: Date
  estimatedEndDate: Date
}

interface DashboardStats {
  totalPatients: number
  activePatients: number
  avgCompliance: number
  appointmentsToday: number
  alertsCount: number
  revenue: number
  monthlyGrowth: number
  satisfactionScore: number
}

interface Alert {
  id: string
  patientId: string
  patientName: string
  type: 'compliance' | 'appointment' | 'payment' | 'technical'
  message: string
  severity: 'low' | 'medium' | 'high'
  createdAt: Date
}

const AdvancedPractitionerDashboard: React.FC = () => {
  const { user } = useAuth()
  const [patients, setPatients] = useState<Patient[]>([])
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    activePatients: 0,
    avgCompliance: 0,
    appointmentsToday: 0,
    alertsCount: 0,
    revenue: 0,
    monthlyGrowth: 0,
    satisfactionScore: 0
  })
  const [selectedTimeRange, setSelectedTimeRange] = useState<'week' | 'month' | 'quarter'>('month')
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRisk, setFilterRisk] = useState<'all' | 'low' | 'medium' | 'high'>('all')

  useEffect(() => {
    loadDashboardData()
  }, [selectedTimeRange])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      // Simuler le chargement des données (remplacer par de vraies données Supabase)
      const mockPatients: Patient[] = [
        {
          id: '1',
          name: 'Marie Dubois',
          email: '<EMAIL>',
          phone: '+33 6 12 34 56 78',
          treatmentProgress: 65,
          complianceRate: 92,
          lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          riskLevel: 'low',
          nextAppointment: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          currentAligner: 13,
          totalAligners: 20,
          treatmentType: 'Invisalign',
          startDate: new Date('2024-06-01'),
          estimatedEndDate: new Date('2025-04-01')
        },
        {
          id: '2',
          name: 'Pierre Martin',
          email: '<EMAIL>',
          phone: '+33 6 98 76 54 32',
          treatmentProgress: 45,
          complianceRate: 78,
          lastActivity: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          riskLevel: 'medium',
          nextAppointment: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          currentAligner: 9,
          totalAligners: 20,
          treatmentType: 'Aligneurs',
          startDate: new Date('2024-08-15'),
          estimatedEndDate: new Date('2025-06-15')
        },
        {
          id: '3',
          name: 'Sophie Laurent',
          email: '<EMAIL>',
          phone: '+33 6 11 22 33 44',
          treatmentProgress: 25,
          complianceRate: 58,
          lastActivity: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
          riskLevel: 'high',
          currentAligner: 5,
          totalAligners: 20,
          treatmentType: 'Invisalign',
          startDate: new Date('2024-10-01'),
          estimatedEndDate: new Date('2025-08-01')
        }
      ]

      const mockAlerts: Alert[] = [
        {
          id: '1',
          patientId: '3',
          patientName: 'Sophie Laurent',
          type: 'compliance',
          message: 'Conformité faible (58%) - Intervention recommandée',
          severity: 'high',
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
          id: '2',
          patientId: '2',
          patientName: 'Pierre Martin',
          type: 'appointment',
          message: 'Rendez-vous manqué - Reprogrammation nécessaire',
          severity: 'medium',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        }
      ]

      const mockStats: DashboardStats = {
        totalPatients: 45,
        activePatients: 38,
        avgCompliance: 82,
        appointmentsToday: 6,
        alertsCount: 3,
        revenue: 15420,
        monthlyGrowth: 12.5,
        satisfactionScore: 4.7
      }

      setPatients(mockPatients)
      setAlerts(mockAlerts)
      setStats(mockStats)
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error)
      notify.error('Erreur lors du chargement des données')
    } finally {
      setLoading(false)
    }
  }

  const getRiskColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
    }
  }

  const getComplianceColor = (rate: number) => {
    if (rate >= 85) return 'text-green-600'
    if (rate >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low': return 'bg-blue-100 text-blue-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-red-100 text-red-800'
    }
  }

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterRisk === 'all' || patient.riskLevel === filterRisk
    return matchesSearch && matchesFilter
  })

  const complianceData = [
    { name: 'Lun', compliance: 85, patients: 12 },
    { name: 'Mar', compliance: 88, patients: 15 },
    { name: 'Mer', compliance: 82, patients: 18 },
    { name: 'Jeu', compliance: 90, patients: 14 },
    { name: 'Ven', compliance: 87, patients: 16 },
    { name: 'Sam', compliance: 79, patients: 8 },
    { name: 'Dim', compliance: 83, patients: 10 }
  ]

  const riskDistribution = [
    { name: 'Faible', value: 70, color: '#10B981' },
    { name: 'Moyen', value: 20, color: '#F59E0B' },
    { name: 'Élevé', value: 10, color: '#EF4444' }
  ]

  const revenueData = [
    { month: 'Jan', revenue: 12000, patients: 35 },
    { month: 'Fév', revenue: 13500, patients: 38 },
    { month: 'Mar', revenue: 14200, patients: 42 },
    { month: 'Avr', revenue: 15420, patients: 45 }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Bonjour Dr. {user?.fullName?.split(' ').pop()}
            </h1>
            <p className="text-blue-100">
              Dashboard Praticien Avancé - Vue d'ensemble complète de votre cabinet
            </p>
          </div>
          <div className="flex space-x-3">
            <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Exporter</span>
            </button>
            <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Nouveau Patient</span>
            </button>
          </div>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Patients Total</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalPatients}</p>
              <p className="text-sm text-green-600 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +{stats.monthlyGrowth}% ce mois
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-lg">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conformité Moyenne</p>
              <p className="text-3xl font-bold text-gray-900">{stats.avgCompliance}%</p>
              <p className="text-sm text-green-600">Objectif: 85%</p>
            </div>
            <div className="bg-green-100 p-3 rounded-lg">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Revenus Mensuels</p>
              <p className="text-3xl font-bold text-gray-900">{stats.revenue.toLocaleString()}€</p>
              <p className="text-sm text-blue-600">+12.5% vs mois dernier</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-lg">
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Satisfaction</p>
              <p className="text-3xl font-bold text-gray-900">{stats.satisfactionScore}/5</p>
              <p className="text-sm text-yellow-600">Basé sur 127 avis</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-lg">
              <CheckCircle className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Alertes importantes */}
      {alerts.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Bell className="h-5 w-5 text-red-500 mr-2" />
              Alertes Importantes ({alerts.length})
            </h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">
              Voir toutes
            </button>
          </div>
          <div className="space-y-3">
            {alerts.slice(0, 3).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={`h-5 w-5 ${
                    alert.severity === 'high' ? 'text-red-500' : 
                    alert.severity === 'medium' ? 'text-yellow-500' : 'text-blue-500'
                  }`} />
                  <div>
                    <p className="font-medium text-gray-900">{alert.patientName}</p>
                    <p className="text-sm text-gray-600">{alert.message}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                    {alert.severity === 'high' ? 'Urgent' : 
                     alert.severity === 'medium' ? 'Moyen' : 'Info'}
                  </span>
                  <button className="text-blue-600 hover:text-blue-700">
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default AdvancedPractitionerDashboard
