import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import PatientDashboard from '../components/Dashboard/PatientDashboard';
import PractitionerDashboard from '../components/Dashboard/PractitionerDashboard';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (user?.role === 'patient') {
    return <PatientDashboard />;
  }

  if (user?.role === 'practitioner') {
    return <PractitionerDashboard />;
  }

  return (
    <div className="text-center py-8">
      <p className="text-gray-500">Rôle utilisateur non reconnu</p>
    </div>
  );
};

export default DashboardPage;