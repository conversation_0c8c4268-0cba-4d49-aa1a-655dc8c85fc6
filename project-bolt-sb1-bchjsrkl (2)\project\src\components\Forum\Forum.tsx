import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { MessageSquare, Plus, Heart, MessageCircle, Flag, Search } from 'lucide-react';
import { ForumPost, Comment } from '../../types';

const Forum: React.FC = () => {
  const { user } = useAuth();
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [showNewPost, setShowNewPost] = useState(false);
  const [newPost, setNewPost] = useState({ title: '', content: '', category: 'general', isAnonymous: false });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'Toutes les catégories' },
    { id: 'general', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'progress', name: 'Progression' },
    { id: 'tips', name: 'Conseils' },
    { id: 'support', name: 'Soutien' },
    ...(user?.role === 'practitioner' ? [
      { id: 'clinical', name: 'Cas cliniques' },
      { id: 'techniques', name: 'Techniques' }
    ] : [])
  ];

  useEffect(() => {
    const saved = localStorage.getItem('forum_posts');
    if (saved) {
      setPosts(JSON.parse(saved));
    }
  }, []);

  const savePosts = (updatedPosts: ForumPost[]) => {
    setPosts(updatedPosts);
    localStorage.setItem('forum_posts', JSON.stringify(updatedPosts));
  };

  const handleCreatePost = () => {
    if (!newPost.title.trim() || !newPost.content.trim()) return;

    const post: ForumPost = {
      id: Date.now().toString(),
      authorId: user!.id,
      authorName: newPost.isAnonymous ? 'Anonyme' : user!.fullName,
      title: newPost.title,
      content: newPost.content,
      category: newPost.category,
      isAnonymous: newPost.isAnonymous,
      createdAt: new Date(),
      comments: [],
      likes: 0
    };

    savePosts([post, ...posts]);
    setNewPost({ title: '', content: '', category: 'general', isAnonymous: false });
    setShowNewPost(false);
  };

  const handleAddComment = (postId: string, content: string, isAnonymous: boolean) => {
    if (!content.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      authorId: user!.id,
      authorName: isAnonymous ? 'Anonyme' : user!.fullName,
      content,
      createdAt: new Date(),
      isAnonymous
    };

    const updatedPosts = posts.map(post => 
      post.id === postId 
        ? { ...post, comments: [...post.comments, comment] }
        : post
    );

    savePosts(updatedPosts);
  };

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Forum {user?.role === 'practitioner' ? 'Praticiens' : 'Patients'}
          </h2>
          <button
            onClick={() => setShowNewPost(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Nouveau post</span>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans le forum..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>
        </div>

        {/* New Post Modal */}
        {showNewPost && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl p-6 w-full max-w-2xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Créer un nouveau post</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Titre</label>
                  <input
                    type="text"
                    value={newPost.title}
                    onChange={(e) => setNewPost({ ...newPost, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Titre de votre post"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Catégorie</label>
                  <select
                    value={newPost.category}
                    onChange={(e) => setNewPost({ ...newPost, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.slice(1).map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Contenu</label>
                  <textarea
                    value={newPost.content}
                    onChange={(e) => setNewPost({ ...newPost, content: e.target.value })}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Écrivez votre message..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="anonymous"
                    checked={newPost.isAnonymous}
                    onChange={(e) => setNewPost({ ...newPost, isAnonymous: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="anonymous" className="ml-2 text-sm text-gray-700">
                    Publier anonymement
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowNewPost(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                >
                  Annuler
                </button>
                <button
                  onClick={handleCreatePost}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Publier
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Posts List */}
        <div className="space-y-4">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Aucun post trouvé</p>
            </div>
          ) : (
            filteredPosts.map((post) => (
              <PostCard 
                key={post.id} 
                post={post} 
                onAddComment={handleAddComment}
                currentUser={user!}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
};

interface PostCardProps {
  post: ForumPost;
  onAddComment: (postId: string, content: string, isAnonymous: boolean) => void;
  currentUser: any;
}

const PostCard: React.FC<PostCardProps> = ({ post, onAddComment, currentUser }) => {
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [commentAnonymous, setCommentAnonymous] = useState(false);

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      onAddComment(post.id, newComment, commentAnonymous);
      setNewComment('');
      setCommentAnonymous(false);
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-start justify-between mb-3">
        <div>
          <h3 className="font-semibold text-gray-900">{post.title}</h3>
          <p className="text-sm text-gray-600">
            Par {post.authorName} • {post.createdAt.toLocaleDateString()} • {post.category}
          </p>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <Flag className="h-4 w-4" />
        </button>
      </div>

      <p className="text-gray-800 mb-4">{post.content}</p>

      <div className="flex items-center space-x-4 text-sm text-gray-600">
        <button className="flex items-center space-x-1 hover:text-red-600">
          <Heart className="h-4 w-4" />
          <span>{post.likes}</span>
        </button>
        <button 
          onClick={() => setShowComments(!showComments)}
          className="flex items-center space-x-1 hover:text-blue-600"
        >
          <MessageCircle className="h-4 w-4" />
          <span>{post.comments.length}</span>
        </button>
      </div>

      {showComments && (
        <div className="mt-4 space-y-3">
          {post.comments.map((comment) => (
            <div key={comment.id} className="bg-white rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">{comment.authorName}</span>
                <span className="text-xs text-gray-500">{comment.createdAt.toLocaleDateString()}</span>
              </div>
              <p className="text-sm text-gray-800">{comment.content}</p>
            </div>
          ))}

          <div className="bg-white rounded-lg p-3">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Ajouter un commentaire..."
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={`comment-anonymous-${post.id}`}
                  checked={commentAnonymous}
                  onChange={(e) => setCommentAnonymous(e.target.checked)}
                  className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`comment-anonymous-${post.id}`} className="ml-2 text-xs text-gray-600">
                  Anonyme
                </label>
              </div>
              <button
                onClick={handleSubmitComment}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
              >
                Commenter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Forum;