import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Calendar, Check, X, TrendingUp } from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';
import { fr } from 'date-fns/locale';

const ProgressTracker: React.FC = () => {
  const { user } = useAuth();
  const { t, language } = useLanguage();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [progressData, setProgressData] = useState<{ [date: string]: boolean }>({});

  useEffect(() => {
    // Load progress data from localStorage
    const saved = localStorage.getItem(`progress_${user?.id}`);
    if (saved) {
      setProgressData(JSON.parse(saved));
    }
  }, [user?.id]);

  const saveProgress = (newData: { [date: string]: boolean }) => {
    setProgressData(newData);
    localStorage.setItem(`progress_${user?.id}`, JSON.stringify(newData));
  };

  const toggleDay = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const newData = {
      ...progressData,
      [dateKey]: !progressData[dateKey]
    };
    saveProgress(newData);
  };

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const completedDays = Object.values(progressData).filter(Boolean).length;
  const totalDays = Object.keys(progressData).length;
  const progressPercentage = totalDays > 0 ? Math.round((completedDays / totalDays) * 100) : 0;

  const getDayStatus = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return progressData[dateKey];
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">{t('progress.title')}</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Calendar className="h-4 w-4" />
            <span>{format(currentDate, 'MMMM yyyy', { locale: language === 'fr' ? fr : undefined })}</span>
          </div>
        </div>

        {/* Progress Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-500 p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-600">{t('progress.percentage')}</p>
                <p className="text-2xl font-bold text-blue-900">{progressPercentage}%</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-500 p-2 rounded-lg">
                <Check className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-green-600">{t('progress.completed')}</p>
                <p className="text-2xl font-bold text-green-900">{completedDays}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-500 p-2 rounded-lg">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">{t('progress.total')}</p>
                <p className="text-2xl font-bold text-gray-900">{totalDays}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-2 mb-4">
          {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map((day) => (
            <div key={day} className="text-center text-sm font-medium text-gray-500 p-2">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-2">
          {monthDays.map((date) => {
            const dayStatus = getDayStatus(date);
            const isCurrentDay = isToday(date);
            
            return (
              <button
                key={date.toISOString()}
                onClick={() => toggleDay(date)}
                className={`
                  aspect-square p-2 rounded-lg text-sm font-medium transition-all
                  ${isCurrentDay ? 'ring-2 ring-blue-500' : ''}
                  ${dayStatus === true 
                    ? 'bg-green-500 text-white hover:bg-green-600' 
                    : dayStatus === false 
                    ? 'bg-red-100 text-red-600 hover:bg-red-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                <div className="flex flex-col items-center space-y-1">
                  <span>{format(date, 'd')}</span>
                  {dayStatus === true && <Check className="h-3 w-3" />}
                  {dayStatus === false && <X className="h-3 w-3" />}
                </div>
              </button>
            );
          })}
        </div>

        <div className="mt-6 flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded"></div>
            <span className="text-gray-600">Appareil porté</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-100 rounded"></div>
            <span className="text-gray-600">Appareil non porté</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gray-100 rounded"></div>
            <span className="text-gray-600">Non renseigné</span>
          </div>
        </div>
      </div>

      {/* Today's Action */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('progress.today')}</h3>
        <div className="flex items-center justify-between">
          <p className="text-gray-600">Avez-vous porté votre appareil aujourd'hui ?</p>
          <div className="flex space-x-3">
            <button
              onClick={() => toggleDay(new Date())}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                getDayStatus(new Date()) === true
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-green-100 hover:text-green-600'
              }`}
            >
              Oui
            </button>
            <button
              onClick={() => toggleDay(new Date())}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                getDayStatus(new Date()) === false
                  ? 'bg-red-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
              }`}
            >
              Non
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;