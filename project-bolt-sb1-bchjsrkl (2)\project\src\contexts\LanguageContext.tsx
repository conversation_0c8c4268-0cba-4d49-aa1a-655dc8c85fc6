import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Language } from '../types';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  fr: {
    // Navigation
    'nav.dashboard': 'Tableau de bord',
    'nav.patients': 'Patients',
    'nav.appointments': 'Rendez-vous',
    'nav.forum': 'Forum',
    'nav.profile': 'Profil',
    'nav.logout': 'Déconnexion',
    'nav.subscription': 'Abonnement',
    
    // Auth
    'auth.login': 'Connexion',
    'auth.register': '<PERSON><PERSON>er un compte',
    'auth.email': 'Email',
    'auth.password': 'Mot de passe',
    'auth.fullName': 'Nom complet',
    'auth.role': '<PERSON><PERSON>le',
    'auth.patient': 'Patient',
    'auth.practitioner': 'Praticien',
    'auth.practitionerCode': 'Code praticien',
    'auth.loginButton': 'Se connecter',
    'auth.registerButton': 'S\'inscrire',
    'auth.switchToLogin': 'Déjà un compte ? Se connecter',
    'auth.switchToRegister': 'Pas de compte ? S\'inscrire',
    
    // Dashboard
    'dashboard.welcome': 'Bienvenue',
    'dashboard.progress': 'Progression',
    'dashboard.appointments': 'Rendez-vous',
    'dashboard.photos': 'Photos',
    'dashboard.forum': 'Forum',
    'dashboard.teleconsultation': 'Téléconsultation',
    
    // Progress
    'progress.title': 'Suivi du port d\'appareil',
    'progress.today': 'Aujourd\'hui',
    'progress.completed': 'Jours complétés',
    'progress.total': 'Total',
    'progress.percentage': 'Progression',
    
    // Common
    'common.save': 'Enregistrer',
    'common.cancel': 'Annuler',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.add': 'Ajouter',
    'common.search': 'Rechercher',
    'common.filter': 'Filtrer',
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
  },
  en: {
    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.patients': 'Patients',
    'nav.appointments': 'Appointments',
    'nav.forum': 'Forum',
    'nav.profile': 'Profile',
    'nav.logout': 'Logout',
    'nav.subscription': 'Subscription',
    
    // Auth
    'auth.login': 'Login',
    'auth.register': 'Create Account',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.fullName': 'Full Name',
    'auth.role': 'Role',
    'auth.patient': 'Patient',
    'auth.practitioner': 'Practitioner',
    'auth.practitionerCode': 'Practitioner Code',
    'auth.loginButton': 'Sign In',
    'auth.registerButton': 'Sign Up',
    'auth.switchToLogin': 'Already have an account? Sign in',
    'auth.switchToRegister': 'Don\'t have an account? Sign up',
    
    // Dashboard
    'dashboard.welcome': 'Welcome',
    'dashboard.progress': 'Progress',
    'dashboard.appointments': 'Appointments',
    'dashboard.photos': 'Photos',
    'dashboard.forum': 'Forum',
    'dashboard.teleconsultation': 'Teleconsultation',
    
    // Progress
    'progress.title': 'Appliance Wear Tracking',
    'progress.today': 'Today',
    'progress.completed': 'Days Completed',
    'progress.total': 'Total',
    'progress.percentage': 'Progress',
    
    // Common
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('fr');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['fr']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};