@echo off
title Installation Node.js pour OrthProgress
color 0A

echo.
echo ========================================
echo    🚀 INSTALLATION NODE.JS AUTOMATIQUE
echo ========================================
echo.
echo 📥 Ce script va installer Node.js automatiquement
echo    pour faire fonctionner OrthProgress
echo.

REM Vérifier si Node.js est déjà installé
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js est déjà installé!
    node --version
    echo.
    echo 🎉 Vous pouvez maintenant lancer OrthProgress
    echo    Double-cliquez sur lancerpreview.bat
    echo.
    pause
    exit /b 0
)

echo ⚠️  Node.js n'est pas installé sur votre système
echo.
echo 🔄 Téléchargement et installation automatique...
echo.

REM Créer un dossier temporaire
if not exist "%TEMP%\nodejs-install" mkdir "%TEMP%\nodejs-install"
cd /d "%TEMP%\nodejs-install"

echo 📥 Téléchargement de Node.js LTS...
echo    (Cela peut prendre quelques minutes selon votre connexion)
echo.

REM Télécharger Node.js LTS pour Windows x64
powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi' -OutFile 'nodejs-installer.msi'}"

if not exist "nodejs-installer.msi" (
    echo ❌ ERREUR: Impossible de télécharger Node.js
    echo.
    echo 💡 Solutions alternatives:
    echo    1. Vérifiez votre connexion internet
    echo    2. Téléchargez manuellement depuis: https://nodejs.org/
    echo    3. Désactivez temporairement votre antivirus
    echo.
    pause
    exit /b 1
)

echo ✅ Téléchargement terminé!
echo.
echo 🔧 Installation de Node.js...
echo    Une fenêtre d'installation va s'ouvrir
echo    Suivez les instructions et acceptez les paramètres par défaut
echo.

REM Lancer l'installateur
start /wait msiexec /i "nodejs-installer.msi" /quiet /norestart

echo.
echo ✅ Installation terminée!
echo.

REM Nettoyer les fichiers temporaires
cd /d "%TEMP%"
rmdir /s /q "nodejs-install" >nul 2>&1

echo 🔄 Mise à jour des variables d'environnement...
echo.

REM Actualiser les variables d'environnement
call refreshenv >nul 2>&1

REM Vérifier l'installation
echo 🔍 Vérification de l'installation...
echo.

REM Attendre un peu pour que les variables d'environnement se mettent à jour
timeout /t 3 /nobreak >nul

node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js installé avec succès!
    echo    Version: 
    node --version
    echo    npm version: 
    npm --version
    echo.
    echo 🎉 INSTALLATION RÉUSSIE!
    echo.
    echo 📋 Prochaines étapes:
    echo    1. Fermez cette fenêtre
    echo    2. Double-cliquez sur lancerpreview.bat
    echo    3. Profitez d'OrthProgress!
    echo.
) else (
    echo ⚠️  Node.js installé mais pas encore accessible
    echo.
    echo 🔄 Actions requises:
    echo    1. Redémarrez votre ordinateur
    echo    2. Ou fermez tous les terminaux/PowerShell
    echo    3. Puis lancez lancerpreview.bat
    echo.
    echo 💡 Si le problème persiste:
    echo    - Redémarrez votre PC
    echo    - Vérifiez que Node.js est dans le PATH
    echo.
)

echo ========================================
echo.
pause
