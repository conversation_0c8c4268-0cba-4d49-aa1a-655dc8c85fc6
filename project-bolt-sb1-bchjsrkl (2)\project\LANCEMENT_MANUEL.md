# 🚀 GUIDE DE LANCEMENT MANUEL ORTHOPROGRESS

## ✅ **DIAGNOSTIC ACTUEL**

**Votre système est maintenant prêt :**
- ✅ Node.js installé : v22.16.0
- ✅ npm installé : v10.9.2
- ✅ PowerShell débloqué
- ✅ Projet complet avec tous les fichiers

## 📋 **LANCEMENT ÉTAPE PAR ÉTAPE**

### **MÉTHODE 1 : Via l'Explorateur Windows (Plus Simple)**

1. **Ouvrez l'Explorateur Windows**
2. **Naviguez vers :** 
   ```
   C:\Users\<USER>\Desktop\orthoprogress\project-bolt-sb1-bchjsrkl (2)\project
   ```
3. **Dans la barre d'adresse, tapez :** `cmd` et appuyez sur Entrée
4. **Une invite de commande s'ouvre dans le bon dossier**
5. **Tapez les commandes suivantes :**

```cmd
npm install
```
*(Attendez que l'installation se termine - 2-3 minutes)*

```cmd
npm run dev
```

6. **L'application se lance !** Ouvrez votre navigateur à : `http://localhost:5173`

### **MÉTHODE 2 : Via PowerShell**

1. **Clic droit sur le bouton Démarrer** → **"Windows PowerShell (Admin)"**
2. **Naviguez vers le projet :**
```powershell
cd "C:\Users\<USER>\Desktop\orthoprogress\project-bolt-sb1-bchjsrkl (2)\project"
```
3. **Installez les dépendances :**
```powershell
npm install
```
4. **Lancez l'application :**
```powershell
npm run dev
```

### **MÉTHODE 3 : Via VS Code (Recommandée pour développeurs)**

1. **Ouvrez VS Code**
2. **File → Open Folder**
3. **Sélectionnez le dossier :** `project-bolt-sb1-bchjsrkl (2)\project`
4. **Ouvrez le terminal intégré :** `Ctrl + `` (backtick)
5. **Tapez :**
```bash
npm install
npm run dev
```

## 🔧 **EN CAS DE PROBLÈME**

### **Erreur "EACCES" ou permissions**
```cmd
npm config set prefix %APPDATA%\npm
npm install -g npm@latest
```

### **Erreur "network timeout"**
```cmd
npm config set registry https://registry.npmjs.org/
npm cache clean --force
npm install
```

### **Erreur "peer dependencies"**
```cmd
npm install --legacy-peer-deps
```

### **Erreur "gyp ERR"**
```cmd
npm install --no-optional
```

## 🎯 **VÉRIFICATION RAPIDE**

Avant de lancer, vérifiez que vous êtes dans le bon dossier :

```cmd
dir
```

Vous devriez voir :
- package.json
- src/
- public/
- index.html
- etc.

## 🌐 **ACCÈS À L'APPLICATION**

Une fois lancée, l'application sera disponible à :
- **URL :** http://localhost:5173
- **Ou :** http://127.0.0.1:5173

### **Comptes de test :**
- **Patient :** `<EMAIL>` / `password123`
- **Praticien :** `<EMAIL>` / `password123`
- **Admin :** `<EMAIL>` / `password123`

## 📱 **FONCTIONNALITÉS DISPONIBLES**

Une fois connecté, vous pourrez tester :
- ✅ Dashboard interactif
- ✅ Suivi de progression
- ✅ Upload de photos
- ✅ Système de gamification
- ✅ Analytics
- ✅ Forum communautaire
- ✅ Téléconsultation
- ✅ Notifications

## 🛑 **ARRÊTER L'APPLICATION**

Pour arrêter l'application :
- **Appuyez sur `Ctrl + C`** dans la fenêtre de commande
- **Ou fermez** la fenêtre de terminal

## 🔄 **RELANCER L'APPLICATION**

Pour relancer plus tard :
1. **Ouvrez une invite de commande** dans le dossier du projet
2. **Tapez :** `npm run dev`
3. **C'est tout !** (pas besoin de refaire `npm install`)

## 📊 **LOGS ET DÉBOGAGE**

Si vous voyez des erreurs, notez :
- Le message d'erreur exact
- À quelle étape ça plante
- Votre version de Windows

## 🎉 **SUCCÈS ATTENDU**

Quand tout fonctionne, vous verrez :
```
  VITE v5.4.2  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

**Et votre navigateur s'ouvrira automatiquement sur OrthProgress !** 🦷✨

---

**Suivez ces étapes et votre application fonctionnera parfaitement !** 🚀
