@echo off
title OrthProgress - Diagnostic et Lancement
color 0A

echo.
echo ========================================
echo    🔍 DIAGNOSTIC ORTHOPROGRESS
echo ========================================
echo.

REM Vérifier Node.js
echo ✅ Vérification Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js non trouvé
    pause
    exit /b 1
)

REM Vérifier npm
echo ✅ Vérification npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm non trouvé
    pause
    exit /b 1
)

REM Vérifier package.json
echo ✅ Vérification package.json...
if not exist "package.json" (
    echo ❌ package.json non trouvé
    pause
    exit /b 1
)

echo ✅ Tous les prérequis sont OK!
echo.

REM Vérifier node_modules
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    echo    Cela peut prendre 2-3 minutes...
    echo.
    
    npm install
    
    if %errorlevel% neq 0 (
        echo ❌ Erreur lors de l'installation
        echo 🔧 Tentative avec --legacy-peer-deps...
        npm install --legacy-peer-deps
        
        if %errorlevel% neq 0 (
            echo ❌ Échec de l'installation
            echo.
            echo 💡 Solutions:
            echo    1. Vérifiez votre connexion internet
            echo    2. Désactivez temporairement l'antivirus
            echo    3. Essayez: npm cache clean --force
            echo.
            pause
            exit /b 1
        )
    )
    
    echo ✅ Dépendances installées!
) else (
    echo ✅ Dépendances déjà présentes
)

echo.
echo 🚀 Lancement de l'application...
echo.
echo 📱 L'application sera disponible à:
echo    http://localhost:5173
echo.
echo 🔄 Ouverture automatique du navigateur...
echo.

REM Lancer l'application
start /b npm run dev

REM Attendre que le serveur démarre
timeout /t 5 /nobreak >nul

REM Ouvrir le navigateur
start http://localhost:5173

echo ========================================
echo    🎉 ORTHOPROGRESS LANCÉ!
echo ========================================
echo.
echo 🔐 Comptes de test:
echo    📧 Patient: <EMAIL> / password123
echo    👨‍⚕️ Praticien: <EMAIL> / password123
echo    🔧 Admin: <EMAIL> / password123
echo.
echo ⏹️  Pour arrêter: Ctrl+C dans cette fenêtre
echo.
echo 📊 Logs du serveur:
echo.

REM Garder la fenêtre ouverte pour voir les logs
pause >nul
