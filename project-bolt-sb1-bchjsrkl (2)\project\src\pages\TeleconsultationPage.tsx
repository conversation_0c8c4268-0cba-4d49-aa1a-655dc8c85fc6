import React from 'react';
import { Video, Phone, MessageSquare, Calendar } from 'lucide-react';

const TeleconsultationPage: React.FC = () => {
  const handleWhatsAppCall = () => {
    window.open('https://wa.me/1234567890', '_blank');
  };

  const handleZoomCall = () => {
    window.open('https://zoom.us/j/1234567890', '_blank');
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Téléconsultation</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 rounded-xl p-6 border border-green-200">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-green-500 p-3 rounded-lg">
                <MessageSquare className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-900">WhatsApp</h3>
                <p className="text-sm text-green-700">Consultation via WhatsApp</p>
              </div>
            </div>
            
            <p className="text-green-800 mb-4">
              Contactez directement votre praticien via WhatsApp pour une consultation rapide 
              ou pour poser vos questions.
            </p>
            
            <button
              onClick={handleWhatsAppCall}
              className="w-full bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 transition-colors flex items-center justify-center space-x-2"
            >
              <MessageSquare className="h-5 w-5" />
              <span>Ouvrir WhatsApp</span>
            </button>
          </div>

          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Video className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900">Zoom</h3>
                <p className="text-sm text-blue-700">Visioconférence Zoom</p>
              </div>
            </div>
            
            <p className="text-blue-800 mb-4">
              Rejoignez une consultation vidéo avec votre praticien pour un suivi 
              personnalisé et détaillé.
            </p>
            
            <button
              onClick={handleZoomCall}
              className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2"
            >
              <Video className="h-5 w-5" />
              <span>Rejoindre Zoom</span>
            </button>
          </div>
        </div>

        <div className="mt-8 bg-gray-50 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Prochains rendez-vous</h3>
          
          <div className="space-y-3">
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">Contrôle mensuel</p>
                    <p className="text-sm text-gray-600">15 janvier 2025 à 14h30</p>
                  </div>
                </div>
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  Confirmé
                </span>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Video className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">Téléconsultation</p>
                    <p className="text-sm text-gray-600">22 janvier 2025 à 16h00</p>
                  </div>
                </div>
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                  En ligne
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <Phone className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800">Urgences</h4>
              <p className="text-sm text-yellow-700 mt-1">
                En cas d'urgence orthodontique, contactez directement votre praticien 
                au <strong>01 23 45 67 89</strong> ou utilisez WhatsApp pour un contact immédiat.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeleconsultationPage;